# Maono - Transformando Ideias em Planos de Ação

## Sobre o Projeto

Maono é um assistente de IA conversacional que atua como um Product Manager sênior, ajudando inovadores, criadores e fundadores a transformar ideias iniciais em documentos de requisitos de produto (PRD) completos e estruturados.

## Como usar

Para executar o projeto localmente:

```sh
# Step 1: Clone o repositório
git clone <YOUR_GIT_URL>

# Step 2: Navegue para o diretório do projeto
cd <YOUR_PROJECT_NAME>

# Step 3: Instale as dependências
npm i

# Step 4: Inicie o servidor de desenvolvimento
npm run dev
```

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## Como fazer deploy do projeto?

Para fazer deploy do projeto, você pode usar qualquer serviço de hospedagem que suporte aplicações React/Vite, como:

- Vercel
- Netlify
- GitHub Pages
- Heroku

Execute `npm run build` para gerar os arquivos de produção na pasta `dist/`.
