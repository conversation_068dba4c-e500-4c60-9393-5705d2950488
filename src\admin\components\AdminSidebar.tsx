import { NavLink } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { 
  LayoutDashboard, 
  Users, 
  BarChart3, 
  Settings, 
  Key, 
  Brain,
  X,
  Shield,
  Activity
} from 'lucide-react';

interface AdminSidebarProps {
  onClose?: () => void;
}

export const AdminSidebar = ({ onClose }: AdminSidebarProps) => {
  const navigation = [
    {
      name: 'Dashboard',
      href: '/admin',
      icon: LayoutDashboard,
      end: true
    },
    {
      name: 'Usuários',
      href: '/admin/users',
      icon: Users
    },
    {
      name: 'Métricas',
      href: '/admin/metrics',
      icon: BarChart3
    },
    {
      name: 'Atividades',
      href: '/admin/activities',
      icon: Activity
    },
    {
      name: 'API Keys',
      href: '/admin/api-keys',
      icon: Key
    },
    {
      name: 'Modelos IA',
      href: '/admin/models',
      icon: Brain
    },
    {
      name: 'Configura<PERSON>õ<PERSON>',
      href: '/admin/settings',
      icon: Settings
    }
  ];

  return (
    <div className="flex h-full flex-col bg-card border-r border-border">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-border">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-lg bg-primary flex items-center justify-center">
            <Shield className="h-4 w-4 text-primary-foreground" />
          </div>
          <div>
            <h1 className="text-lg font-bold text-foreground">Admin Panel</h1>
            <p className="text-xs text-muted-foreground">Maono</p>
          </div>
        </div>
        {onClose && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="lg:hidden"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navigation.map((item) => (
          <NavLink
            key={item.name}
            to={item.href}
            end={item.end}
            className={({ isActive }) =>
              `flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                isActive
                  ? 'bg-primary text-primary-foreground'
                  : 'text-muted-foreground hover:text-foreground hover:bg-muted'
              }`
            }
            onClick={onClose}
          >
            <item.icon className="h-4 w-4" />
            {item.name}
          </NavLink>
        ))}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-border">
        <div className="text-xs text-muted-foreground text-center">
          <p>Maono Admin v1.0</p>
          <p>Sistema de Administração</p>
        </div>
      </div>
    </div>
  );
};
