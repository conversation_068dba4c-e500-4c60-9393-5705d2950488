import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Users, 
  Database, 
  Trash2, 
  Plus, 
  Eye,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { testDataService } from '@/lib/testDataService';
import { useToast } from '@/hooks/use-toast';

export const TestDataManager = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [showCredentials, setShowCredentials] = useState(false);
  const { toast } = useToast();

  const handleCreateTestData = async () => {
    setIsLoading(true);
    try {
      testDataService.createTestUsers();
      toast({
        title: "Dados de teste criados!",
        description: "Usuários, PRDs e atividades de exemplo foram criados com sucesso.",
      });
    } catch (error) {
      toast({
        title: "Erro",
        description: "Falha ao criar dados de teste.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearTestData = async () => {
    setIsLoading(true);
    try {
      testDataService.clearTestData();
      toast({
        title: "Dados limpos!",
        description: "Todos os dados de teste foram removidos.",
        variant: "destructive"
      });
    } catch (error) {
      toast({
        title: "Erro",
        description: "Falha ao limpar dados de teste.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testCredentials = testDataService.getTestUserCredentials();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Gerenciamento de Dados de Teste
        </CardTitle>
        <CardDescription>
          Crie ou remova dados de exemplo para testar o sistema
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Actions */}
        <div className="flex items-center gap-4">
          <Button 
            onClick={handleCreateTestData} 
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            {isLoading ? 'Criando...' : 'Criar Dados de Teste'}
          </Button>
          
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="destructive" className="flex items-center gap-2">
                <Trash2 className="h-4 w-4" />
                Limpar Dados
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-destructive" />
                  Confirmar Limpeza
                </DialogTitle>
                <DialogDescription>
                  Esta ação irá remover todos os dados do sistema (usuários, PRDs, atividades, configurações). 
                  Esta ação não pode ser desfeita.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button variant="outline">Cancelar</Button>
                <Button variant="destructive" onClick={handleClearTestData} disabled={isLoading}>
                  {isLoading ? 'Limpando...' : 'Confirmar Limpeza'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Button 
            variant="outline" 
            onClick={() => setShowCredentials(!showCredentials)}
            className="flex items-center gap-2"
          >
            <Eye className="h-4 w-4" />
            {showCredentials ? 'Ocultar' : 'Ver'} Credenciais
          </Button>
        </div>

        {/* Test Data Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 border rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Users className="h-4 w-4 text-blue-500" />
              <span className="font-medium">Usuários de Teste</span>
            </div>
            <p className="text-2xl font-bold">5</p>
            <p className="text-sm text-muted-foreground">
              1 Admin + 4 Usuários
            </p>
          </div>
          
          <div className="p-4 border rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Database className="h-4 w-4 text-green-500" />
              <span className="font-medium">PRDs de Exemplo</span>
            </div>
            <p className="text-2xl font-bold">3</p>
            <p className="text-sm text-muted-foreground">
              Diferentes setores
            </p>
          </div>
          
          <div className="p-4 border rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="h-4 w-4 text-purple-500" />
              <span className="font-medium">Atividades</span>
            </div>
            <p className="text-2xl font-bold">5+</p>
            <p className="text-sm text-muted-foreground">
              Logs de exemplo
            </p>
          </div>
        </div>

        {/* Test Credentials */}
        {showCredentials && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Credenciais de Teste</CardTitle>
              <CardDescription>
                Use estas credenciais para fazer login como diferentes usuários
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Email</TableHead>
                    <TableHead>Senha</TableHead>
                    <TableHead>Plano</TableHead>
                    <TableHead>Função</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell><EMAIL></TableCell>
                    <TableCell><code>123456</code></TableCell>
                    <TableCell>
                      <Badge variant="outline">Gratuito</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className="bg-red-500/10 text-red-500">Admin</Badge>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell><EMAIL></TableCell>
                    <TableCell><code>123456</code></TableCell>
                    <TableCell>
                      <Badge className="bg-blue-500/10 text-blue-500">Básico</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className="bg-green-500/10 text-green-500">Usuário</Badge>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell><EMAIL></TableCell>
                    <TableCell><code>123456</code></TableCell>
                    <TableCell>
                      <Badge className="bg-purple-500/10 text-purple-500">Pro</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className="bg-green-500/10 text-green-500">Usuário</Badge>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell><EMAIL></TableCell>
                    <TableCell><code>123456</code></TableCell>
                    <TableCell>
                      <Badge variant="outline">Gratuito</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className="bg-green-500/10 text-green-500">Usuário</Badge>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell><EMAIL></TableCell>
                    <TableCell><code>123456</code></TableCell>
                    <TableCell>
                      <Badge className="bg-blue-500/10 text-blue-500">Básico</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className="bg-green-500/10 text-green-500">Usuário</Badge>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        )}

        {/* Instructions */}
        <div className="p-4 bg-muted/50 rounded-lg">
          <h4 className="font-medium mb-2">Como usar os dados de teste:</h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>1. Clique em "Criar Dados de Teste" para popular o sistema</li>
            <li>2. Use as credenciais acima para fazer login como diferentes usuários</li>
            <li>3. Explore as funcionalidades com dados realistas</li>
            <li>4. Use "Limpar Dados" para resetar o sistema quando necessário</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};
