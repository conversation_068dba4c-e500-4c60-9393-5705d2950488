import { useState, useEffect } from 'react';
import { 
  adminService, 
  SystemMetrics, 
  ApiConfiguration, 
  ModelConfiguration, 
  UserActivity, 
  SystemSettings,
  AdminUser 
} from '../lib/adminService';

// Hook for system metrics
export const useSystemMetrics = () => {
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const refreshMetrics = () => {
    setIsLoading(true);
    const systemMetrics = adminService.getSystemMetrics();
    setMetrics(systemMetrics);
    setIsLoading(false);
  };

  useEffect(() => {
    refreshMetrics();
  }, []);

  const updateMetrics = (newMetrics: Partial<SystemMetrics>) => {
    if (metrics) {
      const updatedMetrics = { ...metrics, ...newMetrics };
      adminService.saveSystemMetrics(updatedMetrics);
      setMetrics(updatedMetrics);
    }
  };

  return {
    metrics,
    isLoading,
    refreshMetrics,
    updateMetrics
  };
};

// Hook for user management
export const useAdminUsers = () => {
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const refreshUsers = () => {
    setIsLoading(true);
    const allUsers = adminService.getAllUsers();
    setUsers(allUsers);
    setIsLoading(false);
  };

  useEffect(() => {
    refreshUsers();
  }, []);

  const updateUserRole = (userId: string, role: AdminUser['role']) => {
    const success = adminService.updateUserRole(userId, role);
    if (success) {
      refreshUsers();
    }
    return success;
  };

  return {
    users,
    isLoading,
    refreshUsers,
    updateUserRole
  };
};

// Hook for API configurations
export const useApiConfigurations = () => {
  const [configs, setConfigs] = useState<ApiConfiguration[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const refreshConfigs = () => {
    setIsLoading(true);
    const apiConfigs = adminService.getApiConfigurations();
    setConfigs(apiConfigs);
    setIsLoading(false);
  };

  useEffect(() => {
    refreshConfigs();
  }, []);

  const saveConfigs = (newConfigs: ApiConfiguration[]) => {
    adminService.saveApiConfigurations(newConfigs);
    setConfigs(newConfigs);
  };

  const addConfig = (config: Omit<ApiConfiguration, 'id' | 'createdAt'>) => {
    const newConfig: ApiConfiguration = {
      ...config,
      id: `config-${Date.now()}`,
      createdAt: new Date().toISOString()
    };
    const updatedConfigs = [...configs, newConfig];
    saveConfigs(updatedConfigs);
  };

  const updateConfig = (id: string, updates: Partial<ApiConfiguration>) => {
    const updatedConfigs = configs.map(config => 
      config.id === id ? { ...config, ...updates } : config
    );
    saveConfigs(updatedConfigs);
  };

  const deleteConfig = (id: string) => {
    const updatedConfigs = configs.filter(config => config.id !== id);
    saveConfigs(updatedConfigs);
  };

  return {
    configs,
    isLoading,
    refreshConfigs,
    addConfig,
    updateConfig,
    deleteConfig
  };
};

// Hook for model configurations
export const useModelConfigurations = () => {
  const [models, setModels] = useState<ModelConfiguration[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const refreshModels = () => {
    setIsLoading(true);
    const modelConfigs = adminService.getModelConfigurations();
    setModels(modelConfigs);
    setIsLoading(false);
  };

  useEffect(() => {
    refreshModels();
  }, []);

  const saveModels = (newModels: ModelConfiguration[]) => {
    adminService.saveModelConfigurations(newModels);
    setModels(newModels);
  };

  const updateModel = (id: string, updates: Partial<ModelConfiguration>) => {
    const updatedModels = models.map(model => 
      model.id === id ? { ...model, ...updates } : model
    );
    saveModels(updatedModels);
  };

  const setDefaultModel = (id: string) => {
    const updatedModels = models.map(model => ({
      ...model,
      isDefault: model.id === id
    }));
    saveModels(updatedModels);
  };

  return {
    models,
    isLoading,
    refreshModels,
    updateModel,
    setDefaultModel
  };
};

// Hook for user activities
export const useUserActivities = () => {
  const [activities, setActivities] = useState<UserActivity[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const refreshActivities = () => {
    setIsLoading(true);
    const userActivities = adminService.getUserActivities();
    setActivities(userActivities);
    setIsLoading(false);
  };

  useEffect(() => {
    refreshActivities();
  }, []);

  const logActivity = (activity: Omit<UserActivity, 'id' | 'timestamp'>) => {
    adminService.logUserActivity(activity);
    refreshActivities();
  };

  return {
    activities,
    isLoading,
    refreshActivities,
    logActivity
  };
};

// Hook for system settings
export const useSystemSettings = () => {
  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const refreshSettings = () => {
    setIsLoading(true);
    const systemSettings = adminService.getSystemSettings();
    setSettings(systemSettings);
    setIsLoading(false);
  };

  useEffect(() => {
    refreshSettings();
  }, []);

  const updateSettings = (newSettings: Partial<SystemSettings>) => {
    if (settings) {
      const updatedSettings = { ...settings, ...newSettings };
      adminService.saveSystemSettings(updatedSettings);
      setSettings(updatedSettings);
    }
  };

  return {
    settings,
    isLoading,
    refreshSettings,
    updateSettings
  };
};

// Hook for admin data management
export const useAdminDataManagement = () => {
  const exportAdminData = (): string => {
    return adminService.exportAdminData();
  };

  const downloadAdminBackup = () => {
    const data = exportAdminData();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `maono-admin-backup-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const clearAdminData = () => {
    adminService.clearAdminData();
  };

  return {
    exportAdminData,
    downloadAdminBackup,
    clearAdminData
  };
};
