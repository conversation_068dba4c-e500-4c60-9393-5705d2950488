// Admin Service for Maono Application
// Manages admin-specific data and operations

import { User, localStorageService } from '@/lib/localStorage';

export interface AdminUser extends User {
  role: 'user' | 'admin';
  lastActivity?: string;
  totalPrdsCreated?: number;
  subscriptionStatus?: 'active' | 'inactive' | 'cancelled';
}

export interface SystemMetrics {
  totalUsers: number;
  activeUsers: number;
  totalPrds: number;
  prdsCreatedToday: number;
  prdsCreatedThisWeek: number;
  prdsCreatedThisMonth: number;
  apiUsage: {
    requests: number;
    cost: number;
    lastUpdated: string;
  };
  systemHealth: 'healthy' | 'warning' | 'critical';
  storageUsed: number; // in MB
}

export interface ApiConfiguration {
  id: string;
  name: string;
  provider: 'openai' | 'anthropic' | 'google';
  apiKey: string;
  model: string;
  maxTokens: number;
  temperature: number;
  isActive: boolean;
  createdAt: string;
  lastUsed?: string;
}

export interface ModelConfiguration {
  id: string;
  name: string;
  provider: string;
  model: string;
  description: string;
  maxTokens: number;
  temperature: number;
  isDefault: boolean;
  isActive: boolean;
}

export interface UserActivity {
  id: string;
  userId: string;
  userName: string;
  action: string;
  timestamp: string;
  details: any;
  ipAddress?: string;
}

export interface SystemSettings {
  maintenanceMode: boolean;
  registrationEnabled: boolean;
  maxFreeUsers: number;
  maxPrdsPerFreeUser: number;
  maxPrdsPerBasicUser: number;
  systemMessage?: string;
  featuresEnabled: {
    chat: boolean;
    prdExport: boolean;
    userRegistration: boolean;
  };
}

class AdminService {
  private readonly ADMIN_KEYS = {
    SYSTEM_METRICS: 'maono_admin_metrics',
    API_CONFIGS: 'maono_admin_api_configs',
    MODEL_CONFIGS: 'maono_admin_model_configs',
    USER_ACTIVITIES: 'maono_admin_activities',
    SYSTEM_SETTINGS: 'maono_admin_settings'
  };

  // System Metrics
  getSystemMetrics(): SystemMetrics {
    const metricsData = localStorage.getItem(this.ADMIN_KEYS.SYSTEM_METRICS);
    if (metricsData) {
      return JSON.parse(metricsData);
    }

    // Calculate metrics from existing data
    const users = this.getAllUsers();
    const prds = localStorageService.getPRDs();
    const today = new Date().toDateString();
    const thisWeek = this.getWeekStart();
    const thisMonth = this.getMonthStart();

    const metrics: SystemMetrics = {
      totalUsers: users.length,
      activeUsers: users.filter(u => u.lastLogin && 
        new Date(u.lastLogin) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      ).length,
      totalPrds: prds.length,
      prdsCreatedToday: prds.filter(p => new Date(p.createdAt).toDateString() === today).length,
      prdsCreatedThisWeek: prds.filter(p => new Date(p.createdAt) >= thisWeek).length,
      prdsCreatedThisMonth: prds.filter(p => new Date(p.createdAt) >= thisMonth).length,
      apiUsage: {
        requests: 0,
        cost: 0,
        lastUpdated: new Date().toISOString()
      },
      systemHealth: 'healthy',
      storageUsed: this.calculateStorageUsage()
    };

    this.saveSystemMetrics(metrics);
    return metrics;
  }

  saveSystemMetrics(metrics: SystemMetrics): void {
    localStorage.setItem(this.ADMIN_KEYS.SYSTEM_METRICS, JSON.stringify(metrics));
  }

  // User Management
  getAllUsers(): AdminUser[] {
    const users = localStorageService.getUser();
    if (!users) return [];
    
    // In a real app, this would fetch all users from database
    // For now, we'll return the current user as admin
    const adminUser: AdminUser = {
      ...users,
      role: 'admin',
      totalPrdsCreated: localStorageService.getPRDs().length,
      subscriptionStatus: 'active'
    };
    
    return [adminUser];
  }

  updateUserRole(userId: string, role: AdminUser['role']): boolean {
    // In a real app, this would update user role in database
    console.log(`Updated user ${userId} role to ${role}`);
    return true;
  }

  // API Configuration
  getApiConfigurations(): ApiConfiguration[] {
    const configsData = localStorage.getItem(this.ADMIN_KEYS.API_CONFIGS);
    if (configsData) {
      return JSON.parse(configsData);
    }

    // Default configuration
    const defaultConfig: ApiConfiguration = {
      id: 'default-openai',
      name: 'OpenAI GPT-4',
      provider: 'openai',
      apiKey: import.meta.env.VITE_OPENAI_KEY || '',
      model: 'gpt-4',
      maxTokens: 2000,
      temperature: 0.7,
      isActive: true,
      createdAt: new Date().toISOString()
    };

    this.saveApiConfigurations([defaultConfig]);
    return [defaultConfig];
  }

  saveApiConfigurations(configs: ApiConfiguration[]): void {
    localStorage.setItem(this.ADMIN_KEYS.API_CONFIGS, JSON.stringify(configs));
  }

  // Model Configuration
  getModelConfigurations(): ModelConfiguration[] {
    const configsData = localStorage.getItem(this.ADMIN_KEYS.MODEL_CONFIGS);
    if (configsData) {
      return JSON.parse(configsData);
    }

    const defaultModels: ModelConfiguration[] = [
      {
        id: 'gpt-4',
        name: 'GPT-4',
        provider: 'OpenAI',
        model: 'gpt-4',
        description: 'Most capable model, best for complex PRD generation',
        maxTokens: 4000,
        temperature: 0.7,
        isDefault: true,
        isActive: true
      },
      {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        provider: 'OpenAI',
        model: 'gpt-3.5-turbo',
        description: 'Faster and more cost-effective for simple PRDs',
        maxTokens: 2000,
        temperature: 0.7,
        isDefault: false,
        isActive: true
      }
    ];

    this.saveModelConfigurations(defaultModels);
    return defaultModels;
  }

  saveModelConfigurations(configs: ModelConfiguration[]): void {
    localStorage.setItem(this.ADMIN_KEYS.MODEL_CONFIGS, JSON.stringify(configs));
  }

  // User Activities
  logUserActivity(activity: Omit<UserActivity, 'id' | 'timestamp'>): void {
    const activities = this.getUserActivities();
    const newActivity: UserActivity = {
      ...activity,
      id: localStorageService.generateId(),
      timestamp: new Date().toISOString()
    };
    
    activities.unshift(newActivity);
    
    // Keep only last 1000 activities
    if (activities.length > 1000) {
      activities.splice(1000);
    }
    
    localStorage.setItem(this.ADMIN_KEYS.USER_ACTIVITIES, JSON.stringify(activities));
  }

  getUserActivities(): UserActivity[] {
    const activitiesData = localStorage.getItem(this.ADMIN_KEYS.USER_ACTIVITIES);
    return activitiesData ? JSON.parse(activitiesData) : [];
  }

  // System Settings
  getSystemSettings(): SystemSettings {
    const settingsData = localStorage.getItem(this.ADMIN_KEYS.SYSTEM_SETTINGS);
    if (settingsData) {
      return JSON.parse(settingsData);
    }

    const defaultSettings: SystemSettings = {
      maintenanceMode: false,
      registrationEnabled: true,
      maxFreeUsers: 1000,
      maxPrdsPerFreeUser: 2,
      maxPrdsPerBasicUser: 10,
      featuresEnabled: {
        chat: true,
        prdExport: true,
        userRegistration: true
      }
    };

    this.saveSystemSettings(defaultSettings);
    return defaultSettings;
  }

  saveSystemSettings(settings: SystemSettings): void {
    localStorage.setItem(this.ADMIN_KEYS.SYSTEM_SETTINGS, JSON.stringify(settings));
  }

  // Utility Methods
  private getWeekStart(): Date {
    const now = new Date();
    const dayOfWeek = now.getDay();
    const diff = now.getDate() - dayOfWeek;
    return new Date(now.setDate(diff));
  }

  private getMonthStart(): Date {
    const now = new Date();
    return new Date(now.getFullYear(), now.getMonth(), 1);
  }

  private calculateStorageUsage(): number {
    let totalSize = 0;
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        totalSize += localStorage[key].length;
      }
    }
    return Math.round(totalSize / 1024 / 1024 * 100) / 100; // Convert to MB
  }

  // Export/Import Admin Data
  exportAdminData(): string {
    const adminData = {
      metrics: this.getSystemMetrics(),
      apiConfigs: this.getApiConfigurations(),
      modelConfigs: this.getModelConfigurations(),
      activities: this.getUserActivities(),
      settings: this.getSystemSettings(),
      exportedAt: new Date().toISOString()
    };
    return JSON.stringify(adminData, null, 2);
  }

  clearAdminData(): void {
    Object.values(this.ADMIN_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
  }
}

export const adminService = new AdminService();
