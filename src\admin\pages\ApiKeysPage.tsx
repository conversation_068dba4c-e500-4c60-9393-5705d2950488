import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { PasswordInput } from '@/components/ui/password-input';
import { Label } from '@/components/ui/label';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Key, 
  Plus, 
  MoreHorizontal, 
  Eye, 
  EyeOff, 
  Trash2,
  CheckCircle,
  XCircle,
  Activity
} from 'lucide-react';
import { useApiConfigurations } from '../hooks/useAdminData';
import { ApiConfiguration } from '../lib/adminService';
import { useToast } from '@/hooks/use-toast';

export const ApiKeysPage = () => {
  const { configs, isLoading, addConfig, updateConfig, deleteConfig } = useApiConfigurations();
  const { toast } = useToast();
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [visibleKeys, setVisibleKeys] = useState<Set<string>>(new Set());
  const [newConfig, setNewConfig] = useState({
    name: '',
    provider: 'openai' as const,
    apiKey: '',
    model: 'gpt-4',
    maxTokens: 2000,
    temperature: 0.7
  });

  const toggleKeyVisibility = (configId: string) => {
    const newVisible = new Set(visibleKeys);
    if (newVisible.has(configId)) {
      newVisible.delete(configId);
    } else {
      newVisible.add(configId);
    }
    setVisibleKeys(newVisible);
  };

  const handleAddConfig = () => {
    if (!newConfig.name || !newConfig.apiKey) {
      toast({
        title: "Erro",
        description: "Nome e chave da API são obrigatórios",
        variant: "destructive"
      });
      return;
    }

    addConfig({
      ...newConfig,
      isActive: true
    });

    setNewConfig({
      name: '',
      provider: 'openai',
      apiKey: '',
      model: 'gpt-4',
      maxTokens: 2000,
      temperature: 0.7
    });
    setShowAddDialog(false);

    toast({
      title: "Sucesso",
      description: "Configuração de API adicionada com sucesso"
    });
  };

  const handleToggleActive = (id: string, isActive: boolean) => {
    updateConfig(id, { isActive: !isActive });
    toast({
      title: "Sucesso",
      description: `Configuração ${!isActive ? 'ativada' : 'desativada'} com sucesso`
    });
  };

  const handleDeleteConfig = (id: string) => {
    deleteConfig(id);
    toast({
      title: "Sucesso",
      description: "Configuração removida com sucesso"
    });
  };

  const maskApiKey = (key: string) => {
    if (key.length <= 8) return key;
    return key.substring(0, 4) + '•'.repeat(key.length - 8) + key.substring(key.length - 4);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-muted-foreground">Carregando configurações...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Gerenciamento de API Keys</h1>
          <p className="text-muted-foreground">Configure e gerencie chaves de API para modelos de IA</p>
        </div>
        <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Nova Configuração
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Adicionar Nova Configuração de API</DialogTitle>
              <DialogDescription>
                Configure uma nova chave de API para integração com modelos de IA
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nome da Configuração</Label>
                <Input
                  id="name"
                  value={newConfig.name}
                  onChange={(e) => setNewConfig({ ...newConfig, name: e.target.value })}
                  placeholder="Ex: OpenAI Produção"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="provider">Provedor</Label>
                <Select value={newConfig.provider} onValueChange={(value: any) => setNewConfig({ ...newConfig, provider: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="openai">OpenAI</SelectItem>
                    <SelectItem value="anthropic">Anthropic</SelectItem>
                    <SelectItem value="google">Google</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="apiKey">Chave da API</Label>
                <PasswordInput
                  id="apiKey"
                  value={newConfig.apiKey}
                  onChange={(e) => setNewConfig({ ...newConfig, apiKey: e.target.value })}
                  placeholder="sk-..."
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="model">Modelo</Label>
                <Input
                  id="model"
                  value={newConfig.model}
                  onChange={(e) => setNewConfig({ ...newConfig, model: e.target.value })}
                  placeholder="gpt-4"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="maxTokens">Max Tokens</Label>
                  <Input
                    id="maxTokens"
                    type="number"
                    value={newConfig.maxTokens}
                    onChange={(e) => setNewConfig({ ...newConfig, maxTokens: parseInt(e.target.value) })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="temperature">Temperature</Label>
                  <Input
                    id="temperature"
                    type="number"
                    step="0.1"
                    min="0"
                    max="2"
                    value={newConfig.temperature}
                    onChange={(e) => setNewConfig({ ...newConfig, temperature: parseFloat(e.target.value) })}
                  />
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowAddDialog(false)}>
                Cancelar
              </Button>
              <Button onClick={handleAddConfig}>
                Adicionar Configuração
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Configurações</CardTitle>
            <Key className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{configs.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Configurações Ativas</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {configs.filter(c => c.isActive).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Uso Hoje</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0</div>
            <p className="text-xs text-muted-foreground">Requisições</p>
          </CardContent>
        </Card>
      </div>

      {/* Configurations Table */}
      <Card>
        <CardHeader>
          <CardTitle>Configurações de API</CardTitle>
          <CardDescription>
            Gerencie todas as configurações de API do sistema
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nome</TableHead>
                <TableHead>Provedor</TableHead>
                <TableHead>Modelo</TableHead>
                <TableHead>Chave da API</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Último Uso</TableHead>
                <TableHead className="text-right">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {configs.map((config) => (
                <TableRow key={config.id}>
                  <TableCell className="font-medium">{config.name}</TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {config.provider.toUpperCase()}
                    </Badge>
                  </TableCell>
                  <TableCell>{config.model}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <code className="text-sm">
                        {visibleKeys.has(config.id) ? config.apiKey : maskApiKey(config.apiKey)}
                      </code>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleKeyVisibility(config.id)}
                      >
                        {visibleKeys.has(config.id) ? 
                          <EyeOff className="h-4 w-4" /> : 
                          <Eye className="h-4 w-4" />
                        }
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={config.isActive ? 
                      'bg-green-500/10 text-green-500' : 'bg-red-500/10 text-red-500'
                    }>
                      {config.isActive ? 'Ativo' : 'Inativo'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {config.lastUsed ? 
                      new Date(config.lastUsed).toLocaleDateString('pt-BR') : 
                      'Nunca'
                    }
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleToggleActive(config.id, config.isActive)}>
                          {config.isActive ? (
                            <>
                              <XCircle className="mr-2 h-4 w-4" />
                              Desativar
                            </>
                          ) : (
                            <>
                              <CheckCircle className="mr-2 h-4 w-4" />
                              Ativar
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleDeleteConfig(config.id)}
                          className="text-destructive"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Excluir
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {configs.length === 0 && (
            <div className="text-center py-8">
              <Key className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">Nenhuma configuração de API encontrada</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
