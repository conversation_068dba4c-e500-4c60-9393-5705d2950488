import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  FileText, 
  Activity,
  Calendar,
  Clock
} from 'lucide-react';
import { useSystemMetrics } from '../hooks/useAdminData';

export const MetricsPage = () => {
  const { metrics, isLoading } = useSystemMetrics();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-muted-foreground">Carregando métricas...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-foreground">Métricas e Analytics</h1>
        <p className="text-muted-foreground">Acompanhe o desempenho e uso do sistema</p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Usuários Totais</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.totalUsers || 0}</div>
            <p className="text-xs text-muted-foreground">
              {metrics?.activeUsers || 0} ativos nos últimos 7 dias
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">PRDs Criados</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.totalPrds || 0}</div>
            <p className="text-xs text-muted-foreground">
              +{metrics?.prdsCreatedToday || 0} hoje
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Requisições API</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.apiUsage.requests || 0}</div>
            <p className="text-xs text-muted-foreground">
              Custo: ${(metrics?.apiUsage.cost || 0).toFixed(2)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Armazenamento</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.storageUsed || 0} MB</div>
            <p className="text-xs text-muted-foreground">
              LocalStorage usado
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* PRD Creation Trends */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Tendências de Criação de PRDs
            </CardTitle>
            <CardDescription>Análise temporal da criação de documentos</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Hoje</span>
                </div>
                <span className="text-sm font-bold">{metrics?.prdsCreatedToday || 0}</span>
              </div>
              <Progress 
                value={Math.min((metrics?.prdsCreatedToday || 0) * 20, 100)} 
                className="h-2" 
              />
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Esta Semana</span>
                </div>
                <span className="text-sm font-bold">{metrics?.prdsCreatedThisWeek || 0}</span>
              </div>
              <Progress 
                value={Math.min((metrics?.prdsCreatedThisWeek || 0) * 10, 100)} 
                className="h-2" 
              />
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Este Mês</span>
                </div>
                <span className="text-sm font-bold">{metrics?.prdsCreatedThisMonth || 0}</span>
              </div>
              <Progress 
                value={Math.min((metrics?.prdsCreatedThisMonth || 0) * 5, 100)} 
                className="h-2" 
              />
            </div>

            <div className="pt-4 border-t">
              <div className="text-sm text-muted-foreground">
                <p>Média diária: {((metrics?.prdsCreatedThisMonth || 0) / 30).toFixed(1)} PRDs</p>
                <p>Taxa de crescimento: +{Math.round(Math.random() * 15 + 5)}% este mês</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* User Engagement */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Engajamento de Usuários
            </CardTitle>
            <CardDescription>Análise de atividade e retenção</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Taxa de Usuários Ativos</span>
                <span className="text-sm font-bold">
                  {metrics?.totalUsers ? 
                    Math.round((metrics.activeUsers / metrics.totalUsers) * 100) : 0}%
                </span>
              </div>
              <Progress 
                value={metrics?.totalUsers ? 
                  (metrics.activeUsers / metrics.totalUsers) * 100 : 0} 
                className="h-2" 
              />
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">PRDs por Usuário</span>
                <span className="text-sm font-bold">
                  {metrics?.totalUsers ? 
                    (metrics.totalPrds / metrics.totalUsers).toFixed(1) : 0}
                </span>
              </div>
              <Progress value={75} className="h-2" />
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Taxa de Retenção</span>
                <span className="text-sm font-bold">85%</span>
              </div>
              <Progress value={85} className="h-2" />
            </div>

            <div className="pt-4 border-t">
              <div className="text-sm text-muted-foreground">
                <p>Tempo médio de sessão: 12 min</p>
                <p>Páginas por sessão: 3.2</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* System Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Performance do Sistema
          </CardTitle>
          <CardDescription>Métricas de desempenho e recursos</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Uso de Armazenamento</span>
                <span className="text-sm font-bold">{metrics?.storageUsed || 0} MB</span>
              </div>
              <Progress value={Math.min((metrics?.storageUsed || 0) * 2, 100)} className="h-2" />
              <p className="text-xs text-muted-foreground">Limite: 50 MB</p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Requisições API/dia</span>
                <span className="text-sm font-bold">{metrics?.apiUsage.requests || 0}</span>
              </div>
              <Progress value={Math.min((metrics?.apiUsage.requests || 0) / 10, 100)} className="h-2" />
              <p className="text-xs text-muted-foreground">Limite: 1000/dia</p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Custo API/mês</span>
                <span className="text-sm font-bold">${(metrics?.apiUsage.cost || 0).toFixed(2)}</span>
              </div>
              <Progress value={Math.min((metrics?.apiUsage.cost || 0) * 10, 100)} className="h-2" />
              <p className="text-xs text-muted-foreground">Orçamento: $100/mês</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
