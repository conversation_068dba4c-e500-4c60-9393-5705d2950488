import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Brain, CheckCircle, Star, Settings } from 'lucide-react';
import { useModelConfigurations } from '../hooks/useAdminData';
import { useToast } from '@/hooks/use-toast';

export const ModelsPage = () => {
  const { models, isLoading, updateModel, setDefaultModel } = useModelConfigurations();
  const { toast } = useToast();

  const handleToggleActive = (id: string, isActive: boolean) => {
    updateModel(id, { isActive: !isActive });
    toast({
      title: "Sucesso",
      description: `Modelo ${!isActive ? 'ativado' : 'desativado'} com sucesso`
    });
  };

  const handleSetDefault = (id: string) => {
    setDefaultModel(id);
    toast({
      title: "Sucesso",
      description: "Modelo padrão atualizado com sucesso"
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-muted-foreground">Carregando modelos...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Configuração de Modelos IA</h1>
          <p className="text-muted-foreground">Configure e gerencie modelos de IA disponíveis no sistema</p>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Modelos</CardTitle>
            <Brain className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{models.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Modelos Ativos</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {models.filter(m => m.isActive).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Modelo Padrão</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">
              {models.find(m => m.isDefault)?.name || 'Nenhum'}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Models Table */}
      <Card>
        <CardHeader>
          <CardTitle>Modelos Disponíveis</CardTitle>
          <CardDescription>
            Configure os modelos de IA que estarão disponíveis para geração de PRDs
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Modelo</TableHead>
                <TableHead>Provedor</TableHead>
                <TableHead>Descrição</TableHead>
                <TableHead>Max Tokens</TableHead>
                <TableHead>Temperature</TableHead>
                <TableHead>Padrão</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {models.map((model) => (
                <TableRow key={model.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Brain className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <div className="font-medium">{model.name}</div>
                        <div className="text-sm text-muted-foreground">{model.model}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {model.provider}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="max-w-xs">
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {model.description}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>{model.maxTokens.toLocaleString()}</TableCell>
                  <TableCell>{model.temperature}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {model.isDefault && (
                        <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSetDefault(model.id)}
                        disabled={model.isDefault}
                      >
                        {model.isDefault ? 'Padrão' : 'Definir como padrão'}
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Switch
                        checked={model.isActive}
                        onCheckedChange={() => handleToggleActive(model.id, model.isActive)}
                      />
                      <span className="text-sm">
                        {model.isActive ? 'Ativo' : 'Inativo'}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="sm">
                      <Settings className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {models.length === 0 && (
            <div className="text-center py-8">
              <Brain className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">Nenhum modelo configurado</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Model Configuration Info */}
      <Card>
        <CardHeader>
          <CardTitle>Informações sobre Configuração</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">Max Tokens</h4>
              <p className="text-sm text-muted-foreground">
                Define o número máximo de tokens que o modelo pode gerar em uma resposta. 
                Valores maiores permitem respostas mais longas, mas consomem mais recursos.
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">Temperature</h4>
              <p className="text-sm text-muted-foreground">
                Controla a criatividade das respostas. Valores baixos (0.1-0.3) geram respostas 
                mais consistentes, valores altos (0.7-1.0) geram respostas mais criativas.
              </p>
            </div>
          </div>
          
          <div className="border-t pt-4">
            <h4 className="font-medium mb-2">Recomendações</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Use GPT-4 para PRDs complexos que requerem maior precisão</li>
              <li>• Use GPT-3.5 Turbo para PRDs simples e respostas mais rápidas</li>
              <li>• Mantenha temperature entre 0.5-0.8 para PRDs balanceados</li>
              <li>• Configure max tokens entre 2000-4000 para PRDs completos</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
