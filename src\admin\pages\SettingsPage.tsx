import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { 
  Settings, 
  Save, 
  AlertTriangle, 
  Shield, 
  Users,
  MessageSquare,
  Download,
  Trash2
} from 'lucide-react';
import { useSystemSettings, useAdminDataManagement } from '../hooks/useAdminData';
import { useToast } from '@/hooks/use-toast';
import { TestDataManager } from '../components/TestDataManager';

export const SettingsPage = () => {
  const { settings, isLoading, updateSettings } = useSystemSettings();
  const { downloadAdminBackup, clearAdminData } = useAdminDataManagement();
  const { toast } = useToast();

  const handleSaveSettings = () => {
    toast({
      title: "Configurações salvas",
      description: "As configurações do sistema foram atualizadas com sucesso."
    });
  };

  const handleDownloadBackup = () => {
    downloadAdminBackup();
    toast({
      title: "Backup iniciado",
      description: "O backup dos dados está sendo baixado."
    });
  };

  const handleClearData = () => {
    if (confirm('Tem certeza que deseja limpar todos os dados administrativos? Esta ação não pode ser desfeita.')) {
      clearAdminData();
      toast({
        title: "Dados limpos",
        description: "Todos os dados administrativos foram removidos.",
        variant: "destructive"
      });
    }
  };

  if (isLoading || !settings) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-muted-foreground">Carregando configurações...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-foreground">Configurações do Sistema</h1>
        <p className="text-muted-foreground">Gerencie configurações globais e preferências do sistema</p>
      </div>

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Status do Sistema
          </CardTitle>
          <CardDescription>Controle o estado operacional do sistema</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base">Modo de Manutenção</Label>
              <p className="text-sm text-muted-foreground">
                Quando ativado, apenas administradores podem acessar o sistema
              </p>
            </div>
            <Switch
              checked={settings.maintenanceMode}
              onCheckedChange={(checked) => updateSettings({ maintenanceMode: checked })}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base">Registro de Novos Usuários</Label>
              <p className="text-sm text-muted-foreground">
                Permitir que novos usuários se registrem no sistema
              </p>
            </div>
            <Switch
              checked={settings.registrationEnabled}
              onCheckedChange={(checked) => updateSettings({ registrationEnabled: checked })}
            />
          </div>

          {settings.maintenanceMode && (
            <div className="p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-yellow-500" />
                <span className="text-sm font-medium text-yellow-700 dark:text-yellow-400">
                  Sistema em modo de manutenção
                </span>
              </div>
              <p className="text-sm text-yellow-600 dark:text-yellow-300 mt-1">
                Apenas administradores podem acessar o sistema no momento.
              </p>
            </div>
          )}

          {settings.systemMessage && (
            <div className="space-y-2">
              <Label htmlFor="systemMessage">Mensagem do Sistema</Label>
              <Textarea
                id="systemMessage"
                value={settings.systemMessage}
                onChange={(e) => updateSettings({ systemMessage: e.target.value })}
                placeholder="Mensagem que será exibida para todos os usuários"
                rows={3}
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* User Limits */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Limites de Usuários
          </CardTitle>
          <CardDescription>Configure limites de uso por tipo de plano</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <Label htmlFor="maxFreeUsers">Máximo de Usuários Gratuitos</Label>
              <Input
                id="maxFreeUsers"
                type="number"
                value={settings.maxFreeUsers}
                onChange={(e) => updateSettings({ maxFreeUsers: parseInt(e.target.value) })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="maxPrdsPerFreeUser">PRDs por Usuário Gratuito</Label>
              <Input
                id="maxPrdsPerFreeUser"
                type="number"
                value={settings.maxPrdsPerFreeUser}
                onChange={(e) => updateSettings({ maxPrdsPerFreeUser: parseInt(e.target.value) })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="maxPrdsPerBasicUser">PRDs por Usuário Básico</Label>
              <Input
                id="maxPrdsPerBasicUser"
                type="number"
                value={settings.maxPrdsPerBasicUser}
                onChange={(e) => updateSettings({ maxPrdsPerBasicUser: parseInt(e.target.value) })}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Feature Flags */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Funcionalidades
          </CardTitle>
          <CardDescription>Ative ou desative funcionalidades do sistema</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base">Sistema de Chat</Label>
              <p className="text-sm text-muted-foreground">
                Permitir conversas com o assistente IA
              </p>
            </div>
            <Switch
              checked={settings.featuresEnabled.chat}
              onCheckedChange={(checked) => updateSettings({ 
                featuresEnabled: { ...settings.featuresEnabled, chat: checked }
              })}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base">Exportação de PRDs</Label>
              <p className="text-sm text-muted-foreground">
                Permitir download de PRDs em formato Markdown
              </p>
            </div>
            <Switch
              checked={settings.featuresEnabled.prdExport}
              onCheckedChange={(checked) => updateSettings({ 
                featuresEnabled: { ...settings.featuresEnabled, prdExport: checked }
              })}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base">Registro de Usuários</Label>
              <p className="text-sm text-muted-foreground">
                Permitir criação de novas contas
              </p>
            </div>
            <Switch
              checked={settings.featuresEnabled.userRegistration}
              onCheckedChange={(checked) => updateSettings({ 
                featuresEnabled: { ...settings.featuresEnabled, userRegistration: checked }
              })}
            />
          </div>
        </CardContent>
      </Card>

      {/* Data Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Gerenciamento de Dados
          </CardTitle>
          <CardDescription>Backup e limpeza de dados do sistema</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h4 className="font-medium">Backup dos Dados</h4>
              <p className="text-sm text-muted-foreground">
                Baixar backup completo dos dados administrativos
              </p>
            </div>
            <Button onClick={handleDownloadBackup} variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Download Backup
            </Button>
          </div>

          <div className="flex items-center justify-between p-4 border border-destructive/20 rounded-lg">
            <div>
              <h4 className="font-medium text-destructive">Limpar Dados Administrativos</h4>
              <p className="text-sm text-muted-foreground">
                Remove todos os dados administrativos (métricas, logs, configurações)
              </p>
            </div>
            <Button onClick={handleClearData} variant="destructive">
              <Trash2 className="h-4 w-4 mr-2" />
              Limpar Dados
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Test Data Management */}
      <TestDataManager />

      {/* Save Button */}
      <div className="flex justify-end">
        <Button onClick={handleSaveSettings} className="flex items-center gap-2">
          <Save className="h-4 w-4" />
          Salvar Configurações
        </Button>
      </div>
    </div>
  );
};
