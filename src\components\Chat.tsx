import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Send, Bot, User } from "lucide-react";
import { Message, localStorageService } from "@/lib/localStorage";
import { usePRDs } from "@/hooks/useLocalStorage";
import { openaiService } from "@/lib/openaiService";
import { useToast } from "@/hooks/use-toast";

interface ChatProps {
  onPrdUpdate?: (prdContent: string) => void;
  initialMessages?: Message[];
  prdId?: string;
}

export const Chat = ({ onPrdUpdate, initialMessages = [], prdId }: ChatProps) => {
  const { savePRD, getPRDById } = usePRDs();
  const { toast } = useToast();
  const [messages, setMessages] = useState<Message[]>(initialMessages);
  const [inputValue, setInputValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Initialize with welcome message if no messages exist
  useEffect(() => {
    if (messages.length === 0 && prdId) {
      const welcomeMessage: Message = {
        id: localStorageService.generateId(),
        text: "Olá! Sou o Maono AI, seu assistente especializado em Product Management. Estou aqui para ajudá-lo a transformar sua ideia em um PRD completo e estruturado. Para começar, me conte: qual é a sua ideia de produto?",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages([welcomeMessage]);

      // Save welcome message
      const currentPRD = getPRDById(prdId);
      if (currentPRD) {
        savePRD({
          ...currentPRD,
          messages: [welcomeMessage],
          updatedAt: new Date().toISOString()
        });
      }
    }
  }, [prdId, messages.length, getPRDById, savePRD]);

  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || !prdId || isTyping) return;

    const userMessage: Message = {
      id: localStorageService.generateId(),
      text: inputValue,
      isUser: true,
      timestamp: new Date(),
    };

    const updatedMessages = [...messages, userMessage];
    setMessages(updatedMessages);
    setInputValue("");
    setIsTyping(true);
    setError(null);

    // Save user message to localStorage
    const currentPRD = getPRDById(prdId);
    if (currentPRD) {
      savePRD({
        ...currentPRD,
        messages: updatedMessages,
        updatedAt: new Date().toISOString()
      });
    }

    try {
      // Convert messages to OpenAI format
      const chatMessages = openaiService.convertMessagesToOpenAI(updatedMessages);

      // Get AI response
      const aiResponse = await openaiService.sendMessage(chatMessages);

      const aiMessage: Message = {
        id: localStorageService.generateId(),
        text: aiResponse,
        isUser: false,
        timestamp: new Date(),
      };

      const finalMessages = [...updatedMessages, aiMessage];
      setMessages(finalMessages);

      // Save AI response to localStorage
      if (currentPRD) {
        savePRD({
          ...currentPRD,
          messages: finalMessages,
          updatedAt: new Date().toISOString()
        });
      }

      // Generate PRD content if conversation has enough context (5+ messages)
      if (finalMessages.length >= 5 && onPrdUpdate) {
        try {
          const prdContent = await openaiService.generatePRDContent(
            openaiService.convertMessagesToOpenAI(finalMessages)
          );
          onPrdUpdate(prdContent);
        } catch (prdError) {
          console.error('Error generating PRD content:', prdError);
        }
      }

    } catch (error) {
      console.error('Error getting AI response:', error);
      setError('Erro ao conectar com a IA. Tente novamente.');

      // Show error message to user
      const errorMessage: Message = {
        id: localStorageService.generateId(),
        text: 'Desculpe, ocorreu um erro ao processar sua mensagem. Por favor, tente novamente.',
        isUser: false,
        timestamp: new Date(),
      };

      const errorMessages = [...updatedMessages, errorMessage];
      setMessages(errorMessages);

      // Save error message
      if (currentPRD) {
        savePRD({
          ...currentPRD,
          messages: errorMessages,
          updatedAt: new Date().toISOString()
        });
      }

      toast({
        title: "Erro de Conexão",
        description: "Não foi possível conectar com a IA. Verifique sua conexão e tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsTyping(false);
    }
  };



  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
    // Allow Shift+Enter for line breaks (default textarea behavior)
  };

  // Auto-resize textarea based on content
  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      const scrollHeight = textareaRef.current.scrollHeight;
      const maxHeight = 120; // Maximum height in pixels (about 5 lines)
      textareaRef.current.style.height = `${Math.min(scrollHeight, maxHeight)}px`;
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value);
    adjustTextareaHeight();
  };

  // Adjust height when input value changes
  useEffect(() => {
    adjustTextareaHeight();
  }, [inputValue]);

  return (
    <div className="flex flex-col h-full bg-card border-r border-border">
      {/* Chat Header */}
      <div className="p-4 border-b border-border bg-muted/30">
        <div className="flex items-center gap-3">
          <Avatar className="h-10 w-10">
            <AvatarFallback className="bg-primary text-primary-foreground">
              <Bot className="h-5 w-5" />
            </AvatarFallback>
          </Avatar>
          <div>
            <h3 className="font-semibold text-foreground">Maono AI</h3>
            <p className="text-sm text-muted-foreground">Product Manager Assistant</p>
          </div>
        </div>
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
        <div className="space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex gap-3 message-enter ${
                message.isUser ? "flex-row-reverse" : "flex-row"
              }`}
            >
              <Avatar className="h-8 w-8 shrink-0">
                <AvatarFallback className={message.isUser ? "bg-accent text-accent-foreground" : "bg-primary text-primary-foreground"}>
                  {message.isUser ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
                </AvatarFallback>
              </Avatar>
              <div
                className={`max-w-[80%] rounded-lg p-3 ${
                  message.isUser
                    ? "bg-accent text-accent-foreground ml-2"
                    : "bg-muted text-foreground mr-2"
                }`}
              >
                <p className="text-sm leading-relaxed">{message.text}</p>
                <span className="text-xs opacity-70 mt-1 block">
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </span>
              </div>
            </div>
          ))}
          
          {isTyping && (
            <div className="flex gap-3">
              <Avatar className="h-8 w-8 shrink-0">
                <AvatarFallback className="bg-primary text-primary-foreground">
                  <Bot className="h-4 w-4" />
                </AvatarFallback>
              </Avatar>
              <div className="bg-muted text-foreground rounded-lg p-3 mr-2">
                <div className="typing-dots">
                  <div className="typing-dot"></div>
                  <div className="typing-dot"></div>
                  <div className="typing-dot"></div>
                </div>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Input Area */}
      <div className="p-4 border-t border-border">
        <div className="flex gap-2 items-end">
          <Textarea
            ref={textareaRef}
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder="Digite sua resposta... (Shift+Enter para nova linha)"
            className="flex-1 min-h-[40px] max-h-[120px] resize-none"
            disabled={isTyping}
            rows={1}
          />
          <Button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isTyping}
            className="shrink-0 mb-1"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};