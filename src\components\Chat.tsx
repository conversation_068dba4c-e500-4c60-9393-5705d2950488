import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Send, Bot, User } from "lucide-react";
import { Message, localStorageService } from "@/lib/localStorage";
import { usePRDs } from "@/hooks/useLocalStorage";

interface ChatProps {
  onPrdUpdate?: (prdContent: string) => void;
  initialMessages?: Message[];
  prdId?: string;
}

export const Chat = ({ onPrdUpdate, initialMessages = [], prdId }: ChatProps) => {
  const { savePRD, getPRDById } = usePRDs();
  const [messages, setMessages] = useState<Message[]>(initialMessages);
  const [inputValue, setInputValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const predefinedQuestions = [
    "Excelente! Agora me ajude a entender melhor: qual problema específico sua ideia resolve?",
    "Perfeito! Quem seria o público-alvo principal dessa solução? Descreva seu usuário ideal.",
    "Ótimo! Quais seriam as 3 funcionalidades mais importantes que seu produto deveria ter?",
    "Interessante! Como você imagina que os usuários irão descobrir e acessar seu produto?",
    "Muito bem! Que resultado ou métrica indicaria que seu produto está sendo bem-sucedido?",
  ];

  const [questionIndex, setQuestionIndex] = useState(0);

  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || !prdId) return;

    const userMessage: Message = {
      id: localStorageService.generateId(),
      text: inputValue,
      isUser: true,
      timestamp: new Date(),
    };

    const updatedMessages = [...messages, userMessage];
    setMessages(updatedMessages);
    setInputValue("");
    setIsTyping(true);

    // Save messages to localStorage
    const currentPRD = getPRDById(prdId);
    if (currentPRD) {
      savePRD({
        ...currentPRD,
        messages: updatedMessages,
        updatedAt: new Date().toISOString()
      });
    }

    // Simulate AI response delay
    setTimeout(() => {
      const nextQuestion = predefinedQuestions[questionIndex % predefinedQuestions.length];
      const aiMessage: Message = {
        id: localStorageService.generateId(),
        text: nextQuestion,
        isUser: false,
        timestamp: new Date(),
      };

      const finalMessages = [...updatedMessages, aiMessage];
      setMessages(finalMessages);
      setIsTyping(false);
      setQuestionIndex(prev => prev + 1);

      // Save AI response to localStorage
      if (currentPRD) {
        savePRD({
          ...currentPRD,
          messages: finalMessages,
          updatedAt: new Date().toISOString()
        });
      }

      // Update PRD content based on conversation
      if (onPrdUpdate) {
        const prdSection = generatePrdSection(userMessage.text, questionIndex);
        onPrdUpdate(prdSection);
      }
    }, 1500);
  };

  const generatePrdSection = (userResponse: string, index: number): string => {
    const sections = [
      `# ${userResponse}\n\n## Visão do Produto\n${userResponse}\n\n`,
      `## Problema a Ser Resolvido\n${userResponse}\n\n`,
      `## Público-Alvo\n${userResponse}\n\n`,
      `## Funcionalidades Principais\n${userResponse}\n\n`,
      `## Estratégia de Distribuição\n${userResponse}\n\n`,
      `## Métricas de Sucesso\n${userResponse}\n\n`,
    ];

    return sections[index] || "";
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="flex flex-col h-full bg-card border-r border-border">
      {/* Chat Header */}
      <div className="p-4 border-b border-border bg-muted/30">
        <div className="flex items-center gap-3">
          <Avatar className="h-10 w-10">
            <AvatarFallback className="bg-primary text-primary-foreground">
              <Bot className="h-5 w-5" />
            </AvatarFallback>
          </Avatar>
          <div>
            <h3 className="font-semibold text-foreground">Maono AI</h3>
            <p className="text-sm text-muted-foreground">Product Manager Assistant</p>
          </div>
        </div>
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
        <div className="space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex gap-3 message-enter ${
                message.isUser ? "flex-row-reverse" : "flex-row"
              }`}
            >
              <Avatar className="h-8 w-8 shrink-0">
                <AvatarFallback className={message.isUser ? "bg-accent text-accent-foreground" : "bg-primary text-primary-foreground"}>
                  {message.isUser ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
                </AvatarFallback>
              </Avatar>
              <div
                className={`max-w-[80%] rounded-lg p-3 ${
                  message.isUser
                    ? "bg-accent text-accent-foreground ml-2"
                    : "bg-muted text-foreground mr-2"
                }`}
              >
                <p className="text-sm leading-relaxed">{message.text}</p>
                <span className="text-xs opacity-70 mt-1 block">
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </span>
              </div>
            </div>
          ))}
          
          {isTyping && (
            <div className="flex gap-3">
              <Avatar className="h-8 w-8 shrink-0">
                <AvatarFallback className="bg-primary text-primary-foreground">
                  <Bot className="h-4 w-4" />
                </AvatarFallback>
              </Avatar>
              <div className="bg-muted text-foreground rounded-lg p-3 mr-2">
                <div className="typing-dots">
                  <div className="typing-dot"></div>
                  <div className="typing-dot"></div>
                  <div className="typing-dot"></div>
                </div>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Input Area */}
      <div className="p-4 border-t border-border">
        <div className="flex gap-2">
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Digite sua resposta..."
            className="flex-1"
            disabled={isTyping}
          />
          <Button 
            onClick={handleSendMessage} 
            disabled={!inputValue.trim() || isTyping}
            className="shrink-0"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};