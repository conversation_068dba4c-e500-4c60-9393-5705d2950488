import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Download, Copy, FileText, Eye, MessageSquare, Sparkles, CheckCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface PrdEditorProps {
  prdContent: string;
  prdTitle?: string;
  onContentChange?: (content: string) => void;
  isGenerating?: boolean;
  messageCount?: number;
}

export const PrdEditor = ({ prdContent, prdTitle = "Product Requirements Document", onContentChange, isGenerating = false, messageCount = 0 }: PrdEditorProps) => {
  const [activeTab, setActiveTab] = useState("preview");
  const [editorContent, setEditorContent] = useState(prdContent);
  const { toast } = useToast();

  useEffect(() => {
    setEditorContent(prdContent);
  }, [prdContent]);

  const handleContentChange = (newContent: string) => {
    setEditorContent(newContent);
    if (onContentChange) {
      onContentChange(newContent);
    }
  };

  const handleCopyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(editorContent);
      toast({
        title: "Copiado!",
        description: "PRD copiado para a área de transferência.",
      });
    } catch (err) {
      toast({
        title: "Erro",
        description: "Não foi possível copiar o conteúdo.",
        variant: "destructive",
      });
    }
  };

  const handleDownload = () => {
    const blob = new Blob([editorContent], { type: "text/markdown" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    const fileName = prdTitle.toLowerCase().replace(/[^a-z0-9]/g, '-') + '.md';
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Download iniciado!",
      description: "Seu PRD está sendo baixado.",
    });
  };

  const renderMarkdown = (text: string) => {
    // Enhanced markdown to HTML conversion for PRD preview
    return text
      .replace(/^# (.*$)/gim, '<h1 class="text-3xl font-bold mb-6 text-primary border-b border-border pb-2">$1</h1>')
      .replace(/^## (.*$)/gim, '<h2 class="text-2xl font-semibold mb-4 text-foreground mt-8 first:mt-0">$1</h2>')
      .replace(/^### (.*$)/gim, '<h3 class="text-xl font-medium mb-3 text-foreground mt-6">$1</h3>')
      .replace(/^#### (.*$)/gim, '<h4 class="text-lg font-medium mb-2 text-foreground mt-4">$1</h4>')
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-foreground">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="italic text-muted-foreground">$1</em>')
      .replace(/^- (.*$)/gim, '<li class="ml-4 mb-1 text-foreground">• $1</li>')
      .replace(/^\d+\. (.*$)/gim, '<li class="ml-4 mb-1 text-foreground list-decimal">$1</li>')
      .replace(/\n\n/g, '</p><p class="mb-4 text-foreground leading-relaxed">')
      .replace(/\n/g, '<br />')
      .replace(/^(.*)$/, '<p class="mb-4 text-foreground leading-relaxed">$1</p>');
  };

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Header */}
      <div className="p-4 border-b border-border bg-card">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="relative">
              <FileText className="h-5 w-5 text-primary" />
              {editorContent && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-background"></div>
              )}
            </div>
            <div>
              <h3 className="font-semibold text-foreground">{prdTitle}</h3>
              <p className="text-sm text-muted-foreground">
                {editorContent
                  ? `PRD completo gerado • ${messageCount} mensagens processadas`
                  : messageCount >= 5
                    ? "Pronto para gerar PRD"
                    : `Coletando informações • ${messageCount}/5+ mensagens`
                }
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopyToClipboard}
              disabled={!editorContent}
            >
              <Copy className="h-4 w-4 mr-2" />
              Copiar
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={handleDownload}
              disabled={!editorContent}
            >
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <div className="px-4 pt-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="preview" className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Preview
            </TabsTrigger>
            <TabsTrigger value="editor" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Editor
            </TabsTrigger>
          </TabsList>
        </div>

        <div className="flex-1 p-4">
          <TabsContent value="preview" className="h-full m-0">
            <Card className="h-full">
              <CardContent className="p-0 h-full">
                <ScrollArea className="h-full p-6">
                  {editorContent ? (
                    <div className="space-y-4">
                      {/* PRD Generated Badge */}
                      <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg">
                        <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                        <div>
                          <p className="text-sm font-medium text-green-800 dark:text-green-200">
                            PRD Gerado com Sucesso!
                          </p>
                          <p className="text-xs text-green-600 dark:text-green-400">
                            Baseado em {messageCount} mensagens da conversa
                          </p>
                        </div>
                      </div>

                      {/* PRD Content */}
                      <div
                        className="prose prose-sm max-w-none dark:prose-invert"
                        dangerouslySetInnerHTML={{ __html: renderMarkdown(editorContent) }}
                      />
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-64 text-center">
                      <div className="max-w-md">
                        {isGenerating ? (
                          <>
                            <div className="relative mb-6">
                              <Sparkles className="h-12 w-12 text-primary mx-auto animate-pulse" />
                              <div className="absolute -top-1 -right-1">
                                <div className="w-4 h-4 bg-primary rounded-full animate-ping"></div>
                              </div>
                            </div>
                            <h3 className="text-lg font-medium text-foreground mb-2">
                              Gerando seu PRD...
                            </h3>
                            <p className="text-muted-foreground mb-4">
                              A IA está analisando sua conversa e criando um documento estruturado
                            </p>
                            <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                              <MessageSquare className="h-4 w-4" />
                              {messageCount} mensagens processadas
                            </div>
                          </>
                        ) : messageCount >= 5 ? (
                          <>
                            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-foreground mb-2">
                              Pronto para gerar seu PRD!
                            </h3>
                            <p className="text-muted-foreground mb-4">
                              Você já tem informações suficientes. Continue a conversa para gerar o documento.
                            </p>
                            <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                              <MessageSquare className="h-4 w-4" />
                              {messageCount} mensagens coletadas
                            </div>
                          </>
                        ) : (
                          <>
                            <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-foreground mb-2">
                              Continue conversando com o Maono
                            </h3>
                            <p className="text-muted-foreground mb-4">
                              Seu PRD será gerado automaticamente após coletarmos informações suficientes sobre seu produto
                            </p>
                            <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                              <MessageSquare className="h-4 w-4" />
                              {messageCount}/5+ mensagens necessárias
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  )}
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="editor" className="h-full m-0">
            <Card className="h-full">
              <CardContent className="p-0 h-full">
                <textarea
                  value={editorContent}
                  onChange={(e) => handleContentChange(e.target.value)}
                  className="w-full h-full p-6 border-0 resize-none focus:outline-none bg-transparent text-sm font-mono"
                  placeholder="Seu PRD em Markdown será gerado aqui conforme você conversa com o Maono..."
                />
              </CardContent>
            </Card>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};