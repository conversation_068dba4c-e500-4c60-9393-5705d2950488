import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Download, Copy, FileText, Eye } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface PrdEditorProps {
  prdContent: string;
  prdTitle?: string;
  onContentChange?: (content: string) => void;
}

export const PrdEditor = ({ prdContent, prdTitle = "Product Requirements Document", onContentChange }: PrdEditorProps) => {
  const [activeTab, setActiveTab] = useState("preview");
  const [editorContent, setEditorContent] = useState(prdContent);
  const { toast } = useToast();

  useEffect(() => {
    setEditorContent(prdContent);
  }, [prdContent]);

  const handleContentChange = (newContent: string) => {
    setEditorContent(newContent);
    if (onContentChange) {
      onContentChange(newContent);
    }
  };

  const handleCopyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(editorContent);
      toast({
        title: "Copiado!",
        description: "PRD copiado para a área de transferência.",
      });
    } catch (err) {
      toast({
        title: "Erro",
        description: "Não foi possível copiar o conteúdo.",
        variant: "destructive",
      });
    }
  };

  const handleDownload = () => {
    const blob = new Blob([editorContent], { type: "text/markdown" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    const fileName = prdTitle.toLowerCase().replace(/[^a-z0-9]/g, '-') + '.md';
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Download iniciado!",
      description: "Seu PRD está sendo baixado.",
    });
  };

  const renderMarkdown = (text: string) => {
    // Simple markdown to HTML conversion for preview
    return text
      .replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold mb-4 text-primary">$1</h1>')
      .replace(/^## (.*$)/gim, '<h2 class="text-xl font-semibold mb-3 text-foreground">$1</h2>')
      .replace(/^### (.*$)/gim, '<h3 class="text-lg font-medium mb-2 text-foreground">$1</h3>')
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
      .replace(/\n/g, '<br />');
  };

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Header */}
      <div className="p-4 border-b border-border bg-card">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <FileText className="h-5 w-5 text-primary" />
            <div>
              <h3 className="font-semibold text-foreground">{prdTitle}</h3>
              <p className="text-sm text-muted-foreground">Gerado automaticamente pela conversa</p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopyToClipboard}
              disabled={!editorContent}
            >
              <Copy className="h-4 w-4 mr-2" />
              Copiar
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={handleDownload}
              disabled={!editorContent}
            >
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <div className="px-4 pt-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="preview" className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Preview
            </TabsTrigger>
            <TabsTrigger value="editor" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Editor
            </TabsTrigger>
          </TabsList>
        </div>

        <div className="flex-1 p-4">
          <TabsContent value="preview" className="h-full m-0">
            <Card className="h-full">
              <CardContent className="p-0 h-full">
                <ScrollArea className="h-full p-6">
                  {editorContent ? (
                    <div 
                      className="prose prose-sm max-w-none"
                      dangerouslySetInnerHTML={{ __html: renderMarkdown(editorContent) }}
                    />
                  ) : (
                    <div className="flex items-center justify-center h-64 text-center">
                      <div>
                        <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-foreground mb-2">
                          Seu PRD aparecerá aqui
                        </h3>
                        <p className="text-muted-foreground">
                          Continue a conversa com o Maono para gerar seu documento
                        </p>
                      </div>
                    </div>
                  )}
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="editor" className="h-full m-0">
            <Card className="h-full">
              <CardContent className="p-0 h-full">
                <textarea
                  value={editorContent}
                  onChange={(e) => handleContentChange(e.target.value)}
                  className="w-full h-full p-6 border-0 resize-none focus:outline-none bg-transparent text-sm font-mono"
                  placeholder="Seu PRD em Markdown será gerado aqui conforme você conversa com o Maono..."
                />
              </CardContent>
            </Card>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};