import { useState, useEffect } from 'react';
import { localStorageService, User, PRD, AppState, UserStats } from '@/lib/localStorage';

// Hook for user authentication state
export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const storedUser = localStorageService.getUser();
    const isAuth = localStorageService.isAuthenticated();
    
    setUser(storedUser);
    setIsAuthenticated(isAuth);
    setIsLoading(false);
  }, []);

  const login = (userData: User, token: string) => {
    localStorageService.setUser(userData);
    localStorageService.setAuthToken(token);
    setUser(userData);
    setIsAuthenticated(true);
  };

  const logout = () => {
    localStorageService.clearUser();
    setUser(null);
    setIsAuthenticated(false);
  };

  const updateUser = (userData: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...userData };
      localStorageService.setUser(updatedUser);
      setUser(updatedUser);
    }
  };

  return {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    updateUser
  };
};

// Hook for PRD management
export const usePRDs = () => {
  const [prds, setPrds] = useState<PRD[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const storedPrds = localStorageService.getPRDs();
    setPrds(storedPrds);
    setIsLoading(false);
  }, []);

  const savePRD = (prd: PRD) => {
    localStorageService.savePRD(prd);
    setPrds(localStorageService.getPRDs());
  };

  const deletePRD = (id: string) => {
    localStorageService.deletePRD(id);
    setPrds(localStorageService.getPRDs());
  };

  const getPRDById = (id: string): PRD | null => {
    return localStorageService.getPRDById(id);
  };

  const createNewPRD = (title: string = 'Novo PRD'): PRD => {
    const user = localStorageService.getUser();
    const newPRD: PRD = {
      id: localStorageService.generateId(),
      title,
      content: '',
      status: 'draft',
      messages: [{
        id: localStorageService.generateId(),
        text: "Olá! Sou o Maono, seu assistente de Product Manager. Vou te ajudar a transformar sua ideia em um Product Requirements Document (PRD) completo. Para começar, me conte: qual é a sua ideia?",
        isUser: false,
        timestamp: new Date(),
      }],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      userId: user?.id || 'anonymous'
    };

    savePRD(newPRD);
    return newPRD;
  };

  return {
    prds,
    isLoading,
    savePRD,
    deletePRD,
    getPRDById,
    createNewPRD
  };
};

// Hook for app state management
export const useAppState = () => {
  const [appState, setAppStateInternal] = useState<AppState>({
    currentView: 'home',
    theme: 'system'
  });

  useEffect(() => {
    const storedState = localStorageService.getAppState();
    setAppStateInternal(storedState);
  }, []);

  const setAppState = (newState: Partial<AppState>) => {
    localStorageService.setAppState(newState);
    setAppStateInternal(prev => ({ ...prev, ...newState }));
  };

  return {
    appState,
    setAppState
  };
};

// Hook for user statistics
export const useUserStats = () => {
  const [stats, setStats] = useState<UserStats>({
    totalPrds: 0,
    completedPrds: 0,
    planUsage: { used: 0, limit: 2 }
  });
  const [isLoading, setIsLoading] = useState(true);

  const refreshStats = () => {
    const userStats = localStorageService.getUserStats();
    setStats(userStats);
    setIsLoading(false);
  };

  useEffect(() => {
    refreshStats();
  }, []);

  return {
    stats,
    isLoading,
    refreshStats
  };
};

// Hook for data export/import
export const useDataManagement = () => {
  const exportData = (): string => {
    return localStorageService.exportData();
  };

  const importData = (jsonData: string): boolean => {
    return localStorageService.importData(jsonData);
  };

  const clearAllData = () => {
    localStorageService.clearAllData();
  };

  const downloadBackup = () => {
    const data = exportData();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `maono-backup-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return {
    exportData,
    importData,
    clearAllData,
    downloadBackup
  };
};
