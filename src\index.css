@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Maono Design System - Professional Dark Blue Theme */
    --background: 210 100% 97%;
    --foreground: 210 20% 15%;

    --card: 0 0% 100%;
    --card-foreground: 210 20% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 20% 15%;

    /* Primary: Deep Navy Blue */
    --primary: 210 100% 12%;
    --primary-foreground: 0 0% 100%;

    /* Secondary: Light Navy */
    --secondary: 210 50% 25%;
    --secondary-foreground: 0 0% 100%;

    /* Muted: Subtle grays */
    --muted: 210 20% 95%;
    --muted-foreground: 210 20% 45%;

    /* Accent: Golden Orange for CTAs and highlights */
    --accent: 42 100% 50%;
    --accent-foreground: 210 100% 12%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;

    --border: 210 20% 88%;
    --input: 210 20% 95%;
    --ring: 42 100% 50%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark mode: Navy Blue as primary background */
    --background: 210 100% 8%;
    --foreground: 0 0% 100%;

    --card: 210 50% 12%;
    --card-foreground: 0 0% 100%;

    --popover: 210 50% 12%;
    --popover-foreground: 0 0% 100%;

    --primary: 42 100% 50%;
    --primary-foreground: 210 100% 8%;

    --secondary: 210 50% 20%;
    --secondary-foreground: 0 0% 100%;

    --muted: 210 50% 15%;
    --muted-foreground: 210 20% 70%;

    --accent: 42 100% 50%;
    --accent-foreground: 210 100% 8%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;

    --border: 210 50% 20%;
    --input: 210 50% 15%;
    --ring: 42 100% 50%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  }
  
  /* Custom gradients for Maono */
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--secondary)));
  }
  
  .gradient-accent {
    background: linear-gradient(135deg, hsl(var(--accent)), hsl(210 80% 60%));
  }
  
  /* Chat message animations */
  .message-enter {
    opacity: 0;
    transform: translateY(10px);
    animation: messageEnter 0.3s ease-out forwards;
  }
  
  @keyframes messageEnter {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Typing indicator */
  .typing-dots {
    display: inline-flex;
    gap: 2px;
  }
  
  .typing-dot {
    width: 4px;
    height: 4px;
    background: hsl(var(--muted-foreground));
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
  }
  
  .typing-dot:nth-child(1) { animation-delay: -0.32s; }
  .typing-dot:nth-child(2) { animation-delay: -0.16s; }
  
  @keyframes typing {
    0%, 80%, 100% {
      transform: scale(0.8);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }
}