// Local Storage Service for Maono Application
// Temporary solution before implementing Node.js backend

export interface User {
  id: string;
  name: string;
  email: string;
  plan: 'free' | 'basic' | 'pro';
  role: 'user' | 'admin';
  createdAt: string;
  lastLogin: string;
}

export interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

export interface PRD {
  id: string;
  title: string;
  content: string;
  status: 'draft' | 'completed';
  messages: Message[];
  createdAt: string;
  updatedAt: string;
  userId: string;
}

export interface AppState {
  currentView: 'home' | 'chat';
  currentPrdId?: string;
  theme: 'light' | 'dark' | 'system';
}

export interface UserStats {
  totalPrds: number;
  completedPrds: number;
  planUsage: {
    used: number;
    limit: number;
  };
}

class LocalStorageService {
  private readonly KEYS = {
    USER: 'maono_user',
    PRDS: 'maono_prds',
    APP_STATE: 'maono_app_state',
    USER_STATS: 'maono_user_stats',
    AUTH_TOKEN: 'maono_auth_token'
  };

  // User Management
  setUser(user: User): void {
    localStorage.setItem(this.KEYS.USER, JSON.stringify(user));
  }

  getUser(): User | null {
    const userData = localStorage.getItem(this.KEYS.USER);
    return userData ? JSON.parse(userData) : null;
  }

  clearUser(): void {
    localStorage.removeItem(this.KEYS.USER);
    localStorage.removeItem(this.KEYS.AUTH_TOKEN);
  }

  setAuthToken(token: string): void {
    localStorage.setItem(this.KEYS.AUTH_TOKEN, token);
  }

  getAuthToken(): string | null {
    return localStorage.getItem(this.KEYS.AUTH_TOKEN);
  }

  isAuthenticated(): boolean {
    return !!this.getAuthToken() && !!this.getUser();
  }

  // PRD Management
  savePRD(prd: PRD): void {
    const prds = this.getPRDs();
    const existingIndex = prds.findIndex(p => p.id === prd.id);
    
    if (existingIndex >= 0) {
      prds[existingIndex] = { ...prd, updatedAt: new Date().toISOString() };
    } else {
      prds.push(prd);
    }
    
    localStorage.setItem(this.KEYS.PRDS, JSON.stringify(prds));
    this.updateUserStats();
  }

  getPRDs(): PRD[] {
    const prdsData = localStorage.getItem(this.KEYS.PRDS);
    if (!prdsData) return [];
    
    const prds = JSON.parse(prdsData);
    // Convert timestamp strings back to Date objects for messages
    return prds.map((prd: PRD) => ({
      ...prd,
      messages: prd.messages.map(msg => ({
        ...msg,
        timestamp: new Date(msg.timestamp)
      }))
    }));
  }

  getPRDById(id: string): PRD | null {
    const prds = this.getPRDs();
    return prds.find(prd => prd.id === id) || null;
  }

  deletePRD(id: string): void {
    const prds = this.getPRDs().filter(prd => prd.id !== id);
    localStorage.setItem(this.KEYS.PRDS, JSON.stringify(prds));
    this.updateUserStats();
  }

  // App State Management
  setAppState(state: Partial<AppState>): void {
    const currentState = this.getAppState();
    const newState = { ...currentState, ...state };
    localStorage.setItem(this.KEYS.APP_STATE, JSON.stringify(newState));
  }

  getAppState(): AppState {
    const stateData = localStorage.getItem(this.KEYS.APP_STATE);
    return stateData ? JSON.parse(stateData) : {
      currentView: 'home',
      theme: 'system'
    };
  }

  // User Statistics
  private updateUserStats(): void {
    const prds = this.getPRDs();
    const stats: UserStats = {
      totalPrds: prds.length,
      completedPrds: prds.filter(prd => prd.status === 'completed').length,
      planUsage: {
        used: prds.length,
        limit: this.getPlanLimit()
      }
    };
    localStorage.setItem(this.KEYS.USER_STATS, JSON.stringify(stats));
  }

  getUserStats(): UserStats {
    const statsData = localStorage.getItem(this.KEYS.USER_STATS);
    if (statsData) {
      return JSON.parse(statsData);
    }
    
    // Calculate stats if not cached
    this.updateUserStats();
    return this.getUserStats();
  }

  private getPlanLimit(): number {
    const user = this.getUser();
    if (!user) return 2;
    
    switch (user.plan) {
      case 'free': return 2;
      case 'basic': return 10;
      case 'pro': return -1; // unlimited
      default: return 2;
    }
  }

  // Utility Methods
  exportData(): string {
    const data = {
      user: this.getUser(),
      prds: this.getPRDs(),
      appState: this.getAppState(),
      userStats: this.getUserStats(),
      exportedAt: new Date().toISOString()
    };
    return JSON.stringify(data, null, 2);
  }

  importData(jsonData: string): boolean {
    try {
      const data = JSON.parse(jsonData);
      
      if (data.user) this.setUser(data.user);
      if (data.prds) localStorage.setItem(this.KEYS.PRDS, JSON.stringify(data.prds));
      if (data.appState) localStorage.setItem(this.KEYS.APP_STATE, JSON.stringify(data.appState));
      
      this.updateUserStats();
      return true;
    } catch (error) {
      console.error('Failed to import data:', error);
      return false;
    }
  }

  clearAllData(): void {
    Object.values(this.KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
  }

  // Generate unique IDs
  generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }
}

export const localStorageService = new LocalStorageService();
