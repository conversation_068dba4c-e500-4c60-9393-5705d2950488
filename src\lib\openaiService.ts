// OpenAI Service for Maono Application
// Handles AI chat functionality and PRD generation

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface OpenAIResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

class OpenAIService {
  private apiKey: string;
  private baseURL = 'https://api.openai.com/v1';
  private model = 'gpt-4';
  
  // System prompt for Maono AI - Product Manager Assistant
  private systemPrompt = `Você é o Maono AI, um assistente especializado em Product Management que ajuda empreendedores e product managers a criar PRDs (Product Requirements Documents) de alta qualidade.

Sua personalidade:
- Profissional, encorajador, inteligente e didático
- Atua como um mentor experiente em Product Management
- Faz perguntas estratégicas para extrair informações valiosas
- Ajuda a estruturar ideias de forma clara e acionável

Seu objetivo:
- Conduzir uma entrevista guiada para entender a ideia do usuário
- Fazer perguntas inteligentes sobre problema, público-alvo, funcionalidades, métricas, etc.
- Ajudar a refinar e detalhar a proposta de valor
- Extrair informações suficientes para gerar um PRD completo

Diretrizes de conversa:
- Faça uma pergunta por vez para não sobrecarregar o usuário
- Seja específico e prático nas suas perguntas
- Use exemplos quando necessário para clarificar
- Mantenha o foco na descoberta do produto
- Seja encorajador e positivo
- Adapte suas perguntas baseado nas respostas anteriores

Áreas principais a explorar:
1. Problema/Oportunidade
2. Público-alvo e personas
3. Proposta de valor
4. Funcionalidades principais
5. Modelo de negócio
6. Métricas de sucesso
7. Estratégia de go-to-market

Responda sempre em português brasileiro e mantenha um tom conversacional e profissional.`;

  constructor() {
    this.apiKey = import.meta.env.VITE_OPENAI_KEY || '';
    if (!this.apiKey) {
      console.warn('OpenAI API key not found in environment variables');
    }
  }

  async sendMessage(messages: ChatMessage[]): Promise<string> {
    if (!this.apiKey) {
      throw new Error('OpenAI API key not configured');
    }

    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            { role: 'system', content: this.systemPrompt },
            ...messages
          ],
          max_tokens: 500,
          temperature: 0.7,
          top_p: 1,
          frequency_penalty: 0,
          presence_penalty: 0,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`OpenAI API error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
      }

      const data: OpenAIResponse = await response.json();
      
      if (!data.choices || data.choices.length === 0) {
        throw new Error('No response from OpenAI API');
      }

      return data.choices[0].message.content.trim();
    } catch (error) {
      console.error('Error calling OpenAI API:', error);
      throw error;
    }
  }

  // Convert chat messages to OpenAI format
  convertMessagesToOpenAI(messages: { text: string; isUser: boolean }[]): ChatMessage[] {
    return messages.map(msg => ({
      role: msg.isUser ? 'user' : 'assistant',
      content: msg.text
    }));
  }

  // Generate PRD content based on conversation
  async generatePRDContent(conversationMessages: ChatMessage[]): Promise<string> {
    const prdPrompt = `Baseado na conversa anterior, gere um PRD (Product Requirements Document) completo e estruturado em Markdown. 

O PRD deve incluir as seguintes seções:
1. # Visão do Produto
2. ## Problema a Ser Resolvido
3. ## Público-Alvo e Personas
4. ## Proposta de Valor
5. ## Funcionalidades Principais
6. ## Requisitos Técnicos
7. ## Métricas de Sucesso
8. ## Roadmap Inicial
9. ## Riscos e Mitigações

Use as informações da conversa para preencher cada seção de forma detalhada e profissional. Se alguma informação não foi discutida, indique como "A ser definido" ou sugira próximos passos.`;

    try {
      const response = await this.sendMessage([
        ...conversationMessages,
        { role: 'user', content: prdPrompt }
      ]);
      
      return response;
    } catch (error) {
      console.error('Error generating PRD content:', error);
      throw error;
    }
  }

  // Get system prompt for documentation
  getSystemPrompt(): string {
    return this.systemPrompt;
  }
}

export const openaiService = new OpenAIService();
