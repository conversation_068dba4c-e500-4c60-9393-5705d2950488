// Test Data Service for Maono Application
// Creates sample users, PRDs, and activities for testing

import { localStorageService, User, PRD, Message } from './localStorage';
import { adminService } from '../admin/lib/adminService';

export interface TestUser extends User {
  password: string; // For testing purposes only
}

class TestDataService {
  private readonly TEST_USERS: TestUser[] = [
    {
      id: 'user-001',
      name: '<PERSON>',
      email: '<EMAIL>',
      password: '123456',
      plan: 'free',
      role: 'admin',
      createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days ago
      lastLogin: new Date().toISOString()
    },
    {
      id: 'user-002',
      name: '<PERSON>',
      email: '<EMAIL>',
      password: '123456',
      plan: 'basic',
      role: 'user',
      createdAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(), // 25 days ago
      lastLogin: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString() // 2 days ago
    },
    {
      id: 'user-003',
      name: '<PERSON>',
      email: '<EMAIL>',
      password: '123456',
      plan: 'pro',
      role: 'user',
      createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(), // 20 days ago
      lastLogin: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString() // 1 hour ago
    },
    {
      id: 'user-004',
      name: 'Maria Oliveira',
      email: '<EMAIL>',
      password: '123456',
      plan: 'free',
      role: 'user',
      createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(), // 15 days ago
      lastLogin: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString() // 5 days ago
    },
    {
      id: 'user-005',
      name: 'Pedro Costa',
      email: '<EMAIL>',
      password: '123456',
      plan: 'basic',
      role: 'user',
      createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(), // 10 days ago
      lastLogin: new Date(Date.now() - 30 * 60 * 1000).toISOString() // 30 minutes ago
    }
  ];

  private readonly SAMPLE_PRDS = [
    {
      title: 'Aplicativo de Delivery Sustentável',
      content: `# PRD: EcoDelivery - Aplicativo de Delivery Sustentável

## 1. Visão do Produto
Criar um aplicativo de delivery que prioriza restaurantes e produtos locais, com foco em sustentabilidade e redução da pegada de carbono.

## 2. Problema
Os aplicativos de delivery atuais não consideram o impacto ambiental das entregas e não incentivam o consumo local.

## 3. Solução
Plataforma que conecta usuários a restaurantes locais, priorizando entregas sustentáveis e oferecendo incentivos para escolhas ecológicas.

## 4. Funcionalidades Principais
- Mapa de restaurantes locais (raio de 5km)
- Sistema de pontuação de sustentabilidade
- Opções de entrega ecológica (bicicleta, elétrico)
- Programa de recompensas verdes
- Rastreamento de pegada de carbono

## 5. Métricas de Sucesso
- Redução de 30% na pegada de carbono por entrega
- 80% dos pedidos de restaurantes locais
- NPS > 8.0
- 50% dos usuários ativos no programa de recompensas`,
      status: 'draft' as const,
      userId: 'user-001'
    },
    {
      title: 'Plataforma de Cursos Online',
      content: `# PRD: EduPlatform - Plataforma de Cursos Online

## 1. Visão do Produto
Plataforma educacional que conecta especialistas a estudantes através de cursos interativos e personalizados.

## 2. Problema
Falta de personalização e interação em plataformas de ensino online existentes.

## 3. Solução
Sistema adaptativo que personaliza o conteúdo baseado no progresso e estilo de aprendizagem do aluno.

## 4. Funcionalidades Principais
- Sistema de recomendação inteligente
- Aulas ao vivo e gravadas
- Exercícios interativos
- Mentoria 1:1
- Certificações reconhecidas
- Comunidade de aprendizagem

## 5. Métricas de Sucesso
- Taxa de conclusão de cursos > 70%
- Satisfação do aluno > 4.5/5
- 90% dos alunos recomendam a plataforma
- Tempo médio de engajamento > 45min/sessão`,
      status: 'completed' as const,
      userId: 'user-002'
    },
    {
      title: 'Sistema de Gestão de Projetos para Startups',
      content: `# PRD: StartupPM - Sistema de Gestão de Projetos

## 1. Visão do Produto
Ferramenta de gestão de projetos especificamente desenhada para as necessidades de startups em crescimento rápido.

## 2. Problema
Ferramentas existentes são muito complexas ou muito simples para startups que precisam de agilidade e escalabilidade.

## 3. Solução
Sistema híbrido que combina metodologias ágeis com planejamento estratégico, adaptando-se ao crescimento da empresa.

## 4. Funcionalidades Principais
- Kanban boards inteligentes
- OKRs integrados
- Automação de workflows
- Analytics de produtividade
- Integração com ferramentas populares
- Templates para diferentes tipos de startup

## 5. Métricas de Sucesso
- Aumento de 40% na produtividade da equipe
- Redução de 50% no tempo de planejamento
- 95% de adoção pela equipe
- ROI positivo em 3 meses`,
      status: 'draft' as const,
      userId: 'user-003'
    }
  ];

  createTestUsers(): void {
    console.log('🔧 Criando usuários de teste...');
    
    // Clear existing data first
    this.clearTestData();
    
    // Create test users
    this.TEST_USERS.forEach(testUser => {
      const { password, ...user } = testUser;
      localStorageService.setUser(user);
      
      // Set auth token for the user
      localStorageService.setAuthToken(`test_token_${user.id}`);
      
      console.log(`✅ Usuário criado: ${user.name} (${user.email}) - Plano: ${user.plan}`);
    });

    // Create sample PRDs
    this.createSamplePRDs();
    
    // Create sample activities
    this.createSampleActivities();
    
    // Update system metrics
    this.updateSystemMetrics();
    
    console.log('🎉 Dados de teste criados com sucesso!');
  }

  private createSamplePRDs(): void {
    console.log('📄 Criando PRDs de exemplo...');
    
    this.SAMPLE_PRDS.forEach((prdData, index) => {
      const messages: Message[] = [
        {
          id: localStorageService.generateId(),
          text: "Olá! Sou o Maono, seu assistente de Product Manager. Vou te ajudar a transformar sua ideia em um Product Requirements Document (PRD) completo. Para começar, me conte: qual é a sua ideia?",
          isUser: false,
          timestamp: new Date(Date.now() - (index + 1) * 24 * 60 * 60 * 1000)
        },
        {
          id: localStorageService.generateId(),
          text: `Quero criar ${prdData.title.toLowerCase()}`,
          isUser: true,
          timestamp: new Date(Date.now() - (index + 1) * 24 * 60 * 60 * 1000 + 5 * 60 * 1000)
        },
        {
          id: localStorageService.generateId(),
          text: "Excelente ideia! Vou te ajudar a estruturar esse projeto. Primeiro, me conte qual problema específico você quer resolver com essa solução?",
          isUser: false,
          timestamp: new Date(Date.now() - (index + 1) * 24 * 60 * 60 * 1000 + 10 * 60 * 1000)
        }
      ];

      const prd: PRD = {
        id: localStorageService.generateId(),
        title: prdData.title,
        content: prdData.content,
        status: prdData.status,
        messages: messages,
        createdAt: new Date(Date.now() - (index + 1) * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - (index + 1) * 12 * 60 * 60 * 1000).toISOString(),
        userId: prdData.userId
      };

      localStorageService.savePRD(prd);
      console.log(`✅ PRD criado: ${prd.title}`);
    });
  }

  private createSampleActivities(): void {
    console.log('📊 Criando atividades de exemplo...');
    
    const activities = [
      {
        userId: 'user-001',
        userName: 'Carlos César',
        action: 'Fez login no sistema',
        details: { ip: '*************' }
      },
      {
        userId: 'user-002',
        userName: 'Ana Silva',
        action: 'Criou novo PRD: Plataforma de Cursos Online',
        details: { prdId: 'prd-002' }
      },
      {
        userId: 'user-003',
        userName: 'João Santos',
        action: 'Completou PRD: Sistema de Gestão de Projetos',
        details: { prdId: 'prd-003' }
      },
      {
        userId: 'user-001',
        userName: 'Carlos César',
        action: 'Acessou painel administrativo',
        details: { section: 'dashboard' }
      },
      {
        userId: 'user-004',
        userName: 'Maria Oliveira',
        action: 'Exportou PRD em formato Markdown',
        details: { prdId: 'prd-001' }
      }
    ];

    activities.forEach((activity, index) => {
      adminService.logUserActivity({
        ...activity,
        timestamp: new Date(Date.now() - index * 2 * 60 * 60 * 1000).toISOString()
      });
    });

    console.log(`✅ ${activities.length} atividades criadas`);
  }

  private updateSystemMetrics(): void {
    console.log('📈 Atualizando métricas do sistema...');
    
    const metrics = adminService.getSystemMetrics();
    adminService.saveSystemMetrics({
      ...metrics,
      apiUsage: {
        requests: 150,
        cost: 12.50,
        lastUpdated: new Date().toISOString()
      }
    });
    
    console.log('✅ Métricas atualizadas');
  }

  clearTestData(): void {
    console.log('🧹 Limpando dados de teste anteriores...');
    localStorageService.clearAllData();
    adminService.clearAdminData();
    console.log('✅ Dados limpos');
  }

  getTestUsers(): TestUser[] {
    return this.TEST_USERS;
  }

  getTestUserCredentials(): { email: string; password: string }[] {
    return this.TEST_USERS.map(user => ({
      email: user.email,
      password: user.password
    }));
  }

  loginAsTestUser(email: string): boolean {
    const testUser = this.TEST_USERS.find(user => user.email === email);
    if (testUser) {
      const { password, ...user } = testUser;
      localStorageService.setUser(user);
      localStorageService.setAuthToken(`test_token_${user.id}`);
      return true;
    }
    return false;
  }
}

export const testDataService = new TestDataService();
