import { useState, useEffect } from "react";
import { Header } from "@/components/Header";
import { Chat } from "@/components/Chat";
import { PrdEditor } from "@/components/PrdEditor";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Plus, FileText, Clock, AlertCircle } from "lucide-react";
import { useAuth, usePRDs, useAppState, useUserStats } from "@/hooks/useLocalStorage";
import { PRD, localStorageService } from "@/lib/localStorage";
import { useToast } from "@/hooks/use-toast";

export const Dashboard = () => {
  const { user } = useAuth();
  const { prds, createNewPRD, savePRD } = usePRDs();
  const { appState, setAppState } = useAppState();
  const { stats, refreshStats } = useUserStats();
  const { toast } = useToast();

  const [currentPRD, setCurrentPRD] = useState<PRD | null>(null);

  useEffect(() => {
    refreshStats();
  }, [prds, refreshStats]);

  const handlePrdUpdate = (newContent: string) => {
    if (currentPRD) {
      const updatedPRD = {
        ...currentPRD,
        content: newContent, // Replace content instead of appending
        status: 'completed' as const, // Mark as completed when PRD is generated
        updatedAt: new Date().toISOString()
      };
      setCurrentPRD(updatedPRD);
      savePRD(updatedPRD);
    }
  };

  const startNewPrd = () => {
    if (!user) return;

    // Check if user can create more PRDs this month
    if (!localStorageService.canCreatePRD(user.id)) {
      const remaining = localStorageService.getRemainingPrds(user.id);
      toast({
        title: "Limite mensal atingido",
        description: `Você atingiu o limite de 10 PRDs por mês. Restam ${remaining} PRDs este mês.`,
        variant: "destructive"
      });
      return;
    }

    const newPRD = createNewPRD();
    localStorageService.incrementPrdCount(user.id);
    setCurrentPRD(newPRD);
    setAppState({ currentView: 'chat', currentPrdId: newPRD.id });
  };

  const goHome = () => {
    setAppState({ currentView: 'home', currentPrdId: undefined });
    setCurrentPRD(null);
  };

  const openExistingPRD = (prd: PRD) => {
    setCurrentPRD(prd);
    setAppState({ currentView: 'chat', currentPrdId: prd.id });
  };

  if (appState.currentView === 'chat' && currentPRD) {
    return (
      <div className="min-h-screen bg-background">
        <Header onBackToHome={goHome} />
        <div className="h-screen pt-16 flex">
          {/* Left Panel - Chat */}
          <div className="w-1/2 min-w-[400px]">
            <Chat
              onPrdUpdate={handlePrdUpdate}
              initialMessages={currentPRD.messages}
              prdId={currentPRD.id}
            />
          </div>

          {/* Right Panel - PRD Editor */}
          <div className="w-1/2 min-w-[400px]">
            <PrdEditor
              prdContent={currentPRD.content}
              prdTitle={currentPRD.title}
              messageCount={currentPRD.messages.length}
              onContentChange={(content) => {
                const updatedPRD = { ...currentPRD, content, updatedAt: new Date().toISOString() };
                setCurrentPRD(updatedPRD);
                savePRD(updatedPRD);
              }}
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="container max-w-6xl mx-auto p-6 pt-24 space-y-8">
        {/* Welcome Section */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold text-foreground">Bem-vindo, {user?.name || 'Usuário'}!</h1>
          <p className="text-lg text-muted-foreground">Transforme suas ideias em PRDs estruturados</p>
          {user && user.role !== 'admin' && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <AlertCircle className="h-4 w-4" />
              <span>
                {localStorageService.getRemainingPrds(user.id)} PRDs restantes este mês
                (10 PRDs gratuitos por mês durante período promocional)
              </span>
            </div>
          )}
        </div>

        {/* Plan Usage Section */}
        <Card className="bg-card/50 border-border">
          <CardHeader>
            <CardTitle className="text-xl text-foreground">
              Uso do Plano {user?.plan === 'free' ? 'Gratuito' : user?.plan === 'basic' ? 'Básico' : 'Pro'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">
                {stats.planUsage.used} de {stats.planUsage.limit === -1 ? '∞' : stats.planUsage.limit} PRDs
              </span>
              <span className="text-muted-foreground">
                {stats.planUsage.limit === -1 ? '0' : Math.round((stats.planUsage.used / stats.planUsage.limit) * 100)}%
              </span>
            </div>
            <Progress
              value={stats.planUsage.limit === -1 ? 0 : (stats.planUsage.used / stats.planUsage.limit) * 100}
              className="h-2"
            />
            {stats.planUsage.used >= stats.planUsage.limit && stats.planUsage.limit !== -1 ? (
              <p className="text-sm text-destructive">Você atingiu o limite do seu plano</p>
            ) : (
              <p className="text-sm text-muted-foreground">
                {stats.planUsage.limit === -1 ? 'PRDs ilimitados' : `${stats.planUsage.limit - stats.planUsage.used} PRDs restantes`}
              </p>
            )}
          </CardContent>
        </Card>

        {/* PRDs Section */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-foreground">Meus PRDs</h2>
            <Button
              onClick={startNewPrd}
              className="flex items-center gap-2"
              disabled={stats.planUsage.used >= stats.planUsage.limit && stats.planUsage.limit !== -1}
            >
              <Plus className="h-4 w-4" />
              NOVO PRD
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {prds.length === 0 ? (
              <div className="col-span-full text-center py-12">
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">Nenhum PRD criado ainda</h3>
                <p className="text-muted-foreground mb-4">Comece criando seu primeiro PRD com o Maono</p>
                <Button onClick={startNewPrd} className="flex items-center gap-2 mx-auto">
                  <Plus className="h-4 w-4" />
                  Criar Primeiro PRD
                </Button>
              </div>
            ) : (
              prds.map((prd) => (
                <Card
                  key={prd.id}
                  className="bg-card hover:bg-card/80 transition-colors cursor-pointer border-border"
                  onClick={() => openExistingPRD(prd)}
                >
                  <CardHeader className="space-y-3">
                    <div className="flex items-start justify-between">
                      <FileText className="h-5 w-5 text-muted-foreground" />
                      <Badge
                        variant={prd.status === 'completed' ? 'default' : 'secondary'}
                        className={prd.status === 'completed'
                          ? "bg-green-500/10 text-green-500 border-green-500/20"
                          : "bg-muted text-muted-foreground"
                        }
                      >
                        {prd.status === 'completed' ? 'Completo' : 'Rascunho'}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg text-foreground">
                      {prd.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {prd.content ? prd.content.substring(0, 100) + '...' : 'PRD em branco'}
                    </p>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      Atualizado: {new Date(prd.updatedAt).toLocaleDateString('pt-BR', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};