import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PasswordInput } from "@/components/ui/password-input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { Lightbulb, FileText, Zap } from "lucide-react";
import { useAuth } from "@/hooks/useLocalStorage";
import { localStorageService, User } from "@/lib/localStorage";
import { useNavigate } from "react-router-dom";
import { testDataService } from "@/lib/testDataService";

export const Login = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [name, setName] = useState("");
  const { toast } = useToast();
  const { login, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (isAuthenticated) {
      navigate("/dashboard");
    }
  }, [isAuthenticated, navigate]);

  const handleLogin = async (event: React.FormEvent) => {
    event.preventDefault();
    setIsLoading(true);

    // Simulate API call - in real app, this would validate credentials
    setTimeout(() => {
      // Get all test users to validate credentials
      const testUsers = testDataService.getTestUsers();
      const foundUser = testUsers.find(u => u.email === email && u.password === password);

      if (!foundUser) {
        setIsLoading(false);
        toast({
          title: "Erro no login",
          description: "Email ou senha incorretos.",
          variant: "destructive"
        });
        return;
      }

      // Create user data with proper role from test data
      const userData: User = {
        id: foundUser.id,
        name: foundUser.name,
        email: foundUser.email,
        plan: foundUser.plan,
        role: foundUser.role, // Include the role from test data
        createdAt: foundUser.createdAt,
        lastLogin: new Date().toISOString()
      };

      const token = `token_${Date.now()}`; // In real app, this would come from API

      login(userData, token);
      setIsLoading(false);
      toast({
        title: "Login realizado com sucesso!",
        description: "Redirecionando para o dashboard...",
      });
      navigate("/dashboard");
    }, 2000);
  };

  const handleRegister = async (event: React.FormEvent) => {
    event.preventDefault();
    setIsLoading(true);

    // Simulate API call - in real app, this would create new user
    setTimeout(() => {
      const userData: User = {
        id: localStorageService.generateId(),
        name: name,
        email: email,
        plan: 'free',
        createdAt: new Date().toISOString(),
        lastLogin: new Date().toISOString()
      };

      const token = `token_${Date.now()}`; // In real app, this would come from API

      login(userData, token);
      setIsLoading(false);
      toast({
        title: "Conta criada com sucesso!",
        description: "Bem-vindo ao Maono!",
      });
      navigate("/dashboard");
    }, 2000);
  };

  const handleCreateTestData = () => {
    testDataService.createTestUsers();
    toast({
      title: "Dados de teste criados!",
      description: "Use <EMAIL> / 123456 para fazer login como admin.",
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/30">
      <div className="grid lg:grid-cols-2 min-h-screen">
        {/* Left Panel - Hero */}
        <div className="hidden lg:flex flex-col justify-center p-12 bg-primary relative overflow-hidden">
          <div className="absolute inset-0 opacity-10">
            <div className="w-full h-full bg-gradient-to-br from-primary/20 to-accent/20"></div>
          </div>
          <div className="relative z-10">
            <div className="flex items-center gap-3 mb-8">
              <div className="w-12 h-12 rounded-xl bg-accent flex items-center justify-center">
                <span className="text-accent-foreground font-bold text-lg">M</span>
              </div>
              <div>
                <h1 className="text-3xl font-bold text-primary-foreground">Maono</h1>
                <p className="text-primary-foreground/80">AI Product Manager</p>
              </div>
            </div>
            
            <h2 className="text-4xl font-bold text-primary-foreground mb-6 leading-tight">
              Transforme Ideias em 
              <span className="text-accent"> Planos de Ação</span>
            </h2>
            
            <p className="text-xl text-primary-foreground/90 mb-8 leading-relaxed">
              A maior barreira entre uma grande ideia e sua execução é traduzi-la em um plano claro. 
              O Maono resolve isso através de conversas inteligentes.
            </p>

            <div className="space-y-4">
              <div className="flex items-center gap-4 text-primary-foreground/90">
                <div className="w-10 h-10 rounded-lg bg-accent/20 flex items-center justify-center">
                  <Lightbulb className="h-5 w-5 text-accent" />
                </div>
                <div>
                  <h4 className="font-semibold">Entrevista Inteligente</h4>
                  <p className="text-sm opacity-80">Perguntas guiadas como um PM sênior</p>
                </div>
              </div>
              
              <div className="flex items-center gap-4 text-primary-foreground/90">
                <div className="w-10 h-10 rounded-lg bg-accent/20 flex items-center justify-center">
                  <FileText className="h-5 w-5 text-accent" />
                </div>
                <div>
                  <h4 className="font-semibold">PRD Automático</h4>
                  <p className="text-sm opacity-80">Documento técnico gerado em tempo real</p>
                </div>
              </div>
              
              <div className="flex items-center gap-4 text-primary-foreground/90">
                <div className="w-10 h-10 rounded-lg bg-accent/20 flex items-center justify-center">
                  <Zap className="h-5 w-5 text-accent" />
                </div>
                <div>
                  <h4 className="font-semibold">Pronto para Execução</h4>
                  <p className="text-sm opacity-80">Especificações claras para devs e IAs</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Panel - Auth Forms */}
        <div className="flex items-center justify-center p-8">
          <div className="w-full max-w-md">
            <div className="text-center mb-8">
              <div className="flex items-center justify-center gap-3 mb-4 lg:hidden">
                <div className="w-10 h-10 rounded-lg bg-primary flex items-center justify-center">
                  <span className="text-primary-foreground font-bold">M</span>
                </div>
                <h1 className="text-2xl font-bold text-primary">Maono</h1>
              </div>
              <h2 className="text-2xl font-bold text-foreground mb-2">Bem-vindo de volta</h2>
              <p className="text-muted-foreground">
                Entre na sua conta para continuar transformando ideias em realidade
              </p>
            </div>

            <Tabs defaultValue="login" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="login">Entrar</TabsTrigger>
                <TabsTrigger value="register">Criar conta</TabsTrigger>
              </TabsList>
              
              <TabsContent value="login">
                <Card>
                  <CardHeader>
                    <CardTitle>Entrar</CardTitle>
                    <CardDescription>
                      Digite suas credenciais para acessar sua conta
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <form onSubmit={handleLogin} className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="email">Email</Label>
                        <Input
                          id="email"
                          type="email"
                          placeholder="<EMAIL>"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="password">Senha</Label>
                        <PasswordInput
                          id="password"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          required
                        />
                      </div>
                      <Button type="submit" className="w-full" disabled={isLoading}>
                        {isLoading ? "Entrando..." : "Entrar"}
                      </Button>
                    </form>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="register">
                <Card>
                  <CardHeader>
                    <CardTitle>Criar conta</CardTitle>
                    <CardDescription>
                      Comece grátis e transforme suas ideias em PRDs profissionais
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <form onSubmit={handleRegister} className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">Nome completo</Label>
                        <Input
                          id="name"
                          placeholder="Seu nome"
                          value={name}
                          onChange={(e) => setName(e.target.value)}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="register-email">Email</Label>
                        <Input
                          id="register-email"
                          type="email"
                          placeholder="<EMAIL>"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="register-password">Senha</Label>
                        <PasswordInput
                          id="register-password"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          required
                        />
                      </div>
                      <Button type="submit" className="w-full" disabled={isLoading}>
                        {isLoading ? "Criando conta..." : "Criar conta grátis"}
                      </Button>
                    </form>
                    
                    <div className="mt-4 p-4 bg-muted/50 rounded-lg">
                      <p className="text-sm text-muted-foreground text-center">
                        ✨ <strong>Plano Gratuito:</strong> 2 PRDs completos para experimentar
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            {/* Test Data Button */}
            <div className="mt-6 text-center">
              <Button
                variant="outline"
                size="sm"
                onClick={handleCreateTestData}
                className="text-xs"
              >
                🧪 Criar Dados de Teste
              </Button>
              <p className="text-xs text-muted-foreground mt-2">
                Para desenvolvimento e testes
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};