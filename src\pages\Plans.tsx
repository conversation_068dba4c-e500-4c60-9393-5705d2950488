import { useState } from "react";
import { Header } from "@/components/Header";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { 
  Check, 
  Crown, 
  Zap, 
  Star,
  CreditCard,
  Building,
  Users,
  FileText,
  Bot,
  Download,
  Shield,
  Headphones
} from "lucide-react";
import { useAuth } from "@/hooks/useLocalStorage";
import { useNavigate } from "react-router-dom";

interface PlanFeature {
  text: string;
  included: boolean;
}

interface Plan {
  id: string;
  name: string;
  price: number;
  period: string;
  description: string;
  icon: React.ReactNode;
  popular?: boolean;
  features: PlanFeature[];
  buttonText: string;
  buttonVariant: "default" | "outline" | "secondary";
}

export const Plans = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const plans: Plan[] = [
    {
      id: 'free',
      name: 'Gratuito',
      price: 0,
      period: 'para sempre',
      description: 'Perfeito para começar e testar o Maono',
      icon: <Star className="h-6 w-6" />,
      features: [
        { text: 'Até 2 PRDs por mês', included: true },
        { text: 'Chat com IA especializada', included: true },
        { text: 'Templates básicos', included: true },
        { text: 'Exportação em Markdown', included: true },
        { text: 'Suporte por email', included: true },
        { text: 'PRDs ilimitados', included: false },
        { text: 'Templates premium', included: false },
        { text: 'Análise avançada', included: false },
        { text: 'Suporte prioritário', included: false },
      ],
      buttonText: user?.plan === 'free' ? 'Plano Atual' : 'Começar Grátis',
      buttonVariant: user?.plan === 'free' ? 'outline' : 'secondary'
    },
    {
      id: 'basic',
      name: 'Básico',
      price: 29,
      period: 'por mês',
      description: 'Ideal para profissionais e pequenas equipes',
      icon: <Zap className="h-6 w-6" />,
      features: [
        { text: 'Até 10 PRDs por mês', included: true },
        { text: 'Chat com IA especializada', included: true },
        { text: 'Templates básicos e premium', included: true },
        { text: 'Exportação em múltiplos formatos', included: true },
        { text: 'Análise de mercado básica', included: true },
        { text: 'Suporte por email', included: true },
        { text: 'PRDs ilimitados', included: false },
        { text: 'Análise avançada', included: false },
        { text: 'Suporte prioritário', included: false },
      ],
      buttonText: user?.plan === 'basic' ? 'Plano Atual' : 'Escolher Básico',
      buttonVariant: user?.plan === 'basic' ? 'outline' : 'default'
    },
    {
      id: 'pro',
      name: 'Pro',
      price: 79,
      period: 'por mês',
      description: 'Para equipes que precisam de recursos avançados',
      icon: <Crown className="h-6 w-6" />,
      popular: true,
      features: [
        { text: 'PRDs ilimitados', included: true },
        { text: 'Chat com IA especializada', included: true },
        { text: 'Todos os templates', included: true },
        { text: 'Exportação em múltiplos formatos', included: true },
        { text: 'Análise avançada de mercado', included: true },
        { text: 'Colaboração em equipe', included: true },
        { text: 'Integrações com ferramentas', included: true },
        { text: 'Suporte prioritário', included: true },
        { text: 'Consultoria mensal', included: true },
      ],
      buttonText: user?.plan === 'pro' ? 'Plano Atual' : 'Escolher Pro',
      buttonVariant: user?.plan === 'pro' ? 'outline' : 'default'
    }
  ];

  const handleSelectPlan = (planId: string) => {
    if (user?.plan === planId) {
      toast({
        title: "Plano atual",
        description: "Você já está neste plano.",
      });
      return;
    }

    setSelectedPlan(planId);
    setIsProcessing(true);

    // Simulate processing
    setTimeout(() => {
      if (planId === 'free') {
        toast({
          title: "Bem-vindo ao plano gratuito!",
          description: "Você pode começar a usar o Maono agora mesmo.",
        });
        navigate('/dashboard');
      } else {
        // For paid plans, show payment instructions
        toast({
          title: "Redirecionando para pagamento",
          description: "Você será direcionado para finalizar o pagamento via transferência bancária.",
        });
        
        // In a real app, this would redirect to payment processing
        setTimeout(() => {
          toast({
            title: "Instruções de pagamento enviadas",
            description: "Verifique seu email para completar o pagamento.",
          });
          setIsProcessing(false);
          setSelectedPlan(null);
        }, 2000);
      }
    }, 1500);
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="container mx-auto px-6 py-8">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-foreground mb-4">
            Escolha o plano ideal para você
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Transforme suas ideias em PRDs profissionais com a ajuda da IA. 
            Comece grátis e faça upgrade quando precisar.
          </p>
        </div>

        {/* Plans Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto mb-12">
          {plans.map((plan) => (
            <Card 
              key={plan.id} 
              className={`relative ${plan.popular ? 'border-primary shadow-lg scale-105' : ''}`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-primary text-primary-foreground px-3 py-1">
                    Mais Popular
                  </Badge>
                </div>
              )}
              
              <CardHeader className="text-center pb-4">
                <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                  {plan.icon}
                </div>
                <CardTitle className="text-2xl">{plan.name}</CardTitle>
                <CardDescription className="text-base">{plan.description}</CardDescription>
                <div className="mt-4">
                  <span className="text-4xl font-bold text-foreground">
                    R$ {plan.price}
                  </span>
                  <span className="text-muted-foreground ml-2">
                    {plan.period}
                  </span>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <Button
                  onClick={() => handleSelectPlan(plan.id)}
                  variant={plan.buttonVariant}
                  className="w-full"
                  disabled={isProcessing && selectedPlan === plan.id}
                >
                  {isProcessing && selectedPlan === plan.id 
                    ? 'Processando...' 
                    : plan.buttonText
                  }
                </Button>
                
                <Separator />
                
                <ul className="space-y-3">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-3">
                      <Check 
                        className={`h-4 w-4 ${
                          feature.included 
                            ? 'text-green-500' 
                            : 'text-muted-foreground opacity-50'
                        }`} 
                      />
                      <span 
                        className={`text-sm ${
                          feature.included 
                            ? 'text-foreground' 
                            : 'text-muted-foreground line-through'
                        }`}
                      >
                        {feature.text}
                      </span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Payment Information */}
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Informações de Pagamento
              </CardTitle>
              <CardDescription>
                Como funciona o processo de pagamento
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="font-semibold flex items-center gap-2">
                    <Building className="h-4 w-4" />
                    Transferência Bancária
                  </h3>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li>• Pagamento seguro via transferência bancária</li>
                    <li>• Dados bancários enviados por email</li>
                    <li>• Ativação em até 24h após confirmação</li>
                    <li>• Comprovante necessário para ativação</li>
                  </ul>
                </div>
                
                <div className="space-y-4">
                  <h3 className="font-semibold flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    Segurança e Suporte
                  </h3>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li>• Dados protegidos com criptografia</li>
                    <li>• Suporte via email e chat</li>
                    <li>• Cancelamento a qualquer momento</li>
                    <li>• Garantia de 7 dias</li>
                  </ul>
                </div>
              </div>
              
              <Separator />
              
              <div className="text-center">
                <p className="text-sm text-muted-foreground mb-4">
                  Precisa de ajuda para escolher o plano ideal?
                </p>
                <Button variant="outline" className="flex items-center gap-2 mx-auto">
                  <Headphones className="h-4 w-4" />
                  Falar com Especialista
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* FAQ Section */}
        <div className="max-w-2xl mx-auto mt-12 text-center">
          <h2 className="text-2xl font-bold text-foreground mb-4">
            Perguntas Frequentes
          </h2>
          <div className="space-y-4 text-left">
            <details className="group">
              <summary className="flex justify-between items-center cursor-pointer p-4 bg-muted/50 rounded-lg">
                <span className="font-medium">Posso cancelar a qualquer momento?</span>
                <span className="group-open:rotate-180 transition-transform">▼</span>
              </summary>
              <div className="p-4 text-muted-foreground">
                Sim, você pode cancelar sua assinatura a qualquer momento. O acesso continuará até o final do período pago.
              </div>
            </details>
            
            <details className="group">
              <summary className="flex justify-between items-center cursor-pointer p-4 bg-muted/50 rounded-lg">
                <span className="font-medium">Como funciona o período gratuito?</span>
                <span className="group-open:rotate-180 transition-transform">▼</span>
              </summary>
              <div className="p-4 text-muted-foreground">
                O plano gratuito permite criar até 2 PRDs por mês, com acesso completo às funcionalidades básicas da IA.
              </div>
            </details>
            
            <details className="group">
              <summary className="flex justify-between items-center cursor-pointer p-4 bg-muted/50 rounded-lg">
                <span className="font-medium">Posso fazer upgrade ou downgrade?</span>
                <span className="group-open:rotate-180 transition-transform">▼</span>
              </summary>
              <div className="p-4 text-muted-foreground">
                Sim, você pode alterar seu plano a qualquer momento. As mudanças entram em vigor no próximo ciclo de cobrança.
              </div>
            </details>
          </div>
        </div>
      </div>
    </div>
  );
};
